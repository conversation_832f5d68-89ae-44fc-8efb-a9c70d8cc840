#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三红环检测测试脚本
用于验证基于黑框预估第三红环位置的功能
"""

import cv2
import numpy as np
import sys
import os

# 添加当前目录到Python路径，以便导入主模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主模块中的视觉系统类
try:
    from 树莓派opencv识别代码 import RaspberryPiVisionSystem
    print("成功导入视觉系统模块")
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保'树莓派opencv识别代码.py'文件在当前目录中")
    sys.exit(1)

def test_third_ring_detection():
    """测试五个红环检测功能（第三个红环高亮显示）"""
    print("=" * 60)
    print("五个红环检测测试（第三个红环高亮）")
    print("=" * 60)
    print("功能说明:")
    print("1. 基于精确识别的黑框预估所有五个红环位置")
    print("2. 根据题目尺寸规格计算像素/厘米比例")
    print("3. 适应黑框变形（椭圆拟合）")
    print("4. 第三个红环（6cm半径）高亮显示")
    print("5. 显示所有红环的检测结果和置信度")
    print("")
    print("控制说明:")
    print("- 'q': 退出测试")
    print("- 's': 截图保存")
    print("- 't': 切换红环显示")
    print("- 'r': 重置统计")
    print("- '1-5': 切换显示特定红环")
    print("=" * 60)

    # 初始化视觉系统
    try:
        vision = RaspberryPiVisionSystem(camera_id=0)
        print("摄像头初始化成功")
    except Exception as e:
        print(f"摄像头初始化失败: {e}")
        return False

    show_rings = True
    show_specific_ring = 0  # 0=显示所有，1-5=显示特定红环
    frame_count = 0
    detection_stats = {
        'total_frames': 0,
        'black_detected': 0,
        'rings_detected': [0, 0, 0, 0, 0],  # 五个红环的检测次数
        'high_confidence': [0, 0, 0, 0, 0]  # 五个红环的高置信度次数
    }

    try:
        while True:
            ret, frame = vision.cap.read()
            if not ret:
                print("无法读取摄像头画面")
                continue

            frame_count += 1
            detection_stats['total_frames'] += 1

            # 检测目标（包含所有红环信息）
            result = vision.detect_target_with_bbox(frame)
            if len(result) == 5:
                target_x, target_y, black_bbox, red_bbox, all_rings_info = result
            else:
                target_x, target_y, black_bbox, red_bbox = result
                all_rings_info = None

            # 创建显示画面
            display_frame = frame.copy()

            # 绘制画面中心
            cv2.circle(display_frame, (vision.frame_center_x, vision.frame_center_y),
                      5, (0, 255, 255), -1)
            cv2.putText(display_frame, "CENTER",
                       (vision.frame_center_x + 10, vision.frame_center_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

            # 统计更新
            if target_x is not None:
                detection_stats['black_detected'] += 1

            # 绘制黑框检测结果
            if target_x is not None and black_bbox is not None:
                try:
                    if len(black_bbox) == 5:  # 包含轮廓信息
                        x1, y1, x2, y2, contour = black_bbox
                        if contour is not None:
                            cv2.drawContours(display_frame, [contour], -1, (0, 255, 0), 2)
                        cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 1)
                        cv2.putText(display_frame, "BLACK FRAME",
                                   (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                    else:
                        x1, y1, x2, y2 = black_bbox[:4]
                        cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                        cv2.putText(display_frame, "BLACK FRAME",
                                   (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                except Exception as e:
                    print(f"绘制黑框时出错: {e}")

            # 绘制红色靶心
            if red_bbox is not None:
                cv2.circle(display_frame, (target_x, target_y), 15, (0, 0, 255), 2)
                cv2.circle(display_frame, (target_x, target_y), 3, (0, 0, 255), -1)
                cv2.putText(display_frame, "RED TARGET",
                           (target_x + 20, target_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

            # 绘制所有五个红环预估结果
            if show_rings and all_rings_info is not None:
                center = all_rings_info['center']

                # 更新统计信息
                for ring in all_rings_info['rings']:
                    confidence = ring.get('confidence', 0.0)
                    ring_index = ring['index'] - 1  # 转换为0-4索引

                    if confidence > 0.05:
                        detection_stats['rings_detected'][ring_index] += 1
                    if confidence > 0.3:
                        detection_stats['high_confidence'][ring_index] += 1

                # 绘制红环
                for ring in all_rings_info['rings']:
                    ring_radius = ring['radius_pixels']
                    ring_color = ring['color']
                    detected_points = ring.get('detected_points', [])
                    confidence = ring.get('confidence', 0.0)
                    is_third_ring = ring['is_third_ring']
                    ring_index = ring['index']

                    # 检查是否应该显示这个红环
                    should_show = (show_specific_ring == 0 or show_specific_ring == ring_index)

                    if should_show and confidence > 0.02:
                        if is_third_ring:
                            # 第三个红环高亮显示（红色，加粗）
                            cv2.circle(display_frame, center, ring_radius, (0, 0, 255), 5)

                            # 绘制内外边界
                            inner_radius = ring.get('inner_radius', int(ring_radius * 0.8))
                            outer_radius = ring.get('outer_radius', int(ring_radius * 1.2))
                            cv2.circle(display_frame, center, inner_radius, (0, 100, 255), 2)
                            cv2.circle(display_frame, center, outer_radius, (0, 100, 255), 2)

                            # 绘制检测到的红色点（亮黄色，大点）
                            for i, point in enumerate(detected_points[:150]):
                                intensity = int(255 * (1 - i / min(150, len(detected_points))))
                                cv2.circle(display_frame, point, 3, (0, intensity, 255), -1)

                            # 显示第三环特殊标识
                            cv2.putText(display_frame, f"RING 3 (HIGHLIGHT) - {confidence:.3f}",
                                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                        else:
                            # 其他红环正常显示
                            line_thickness = 3 if show_specific_ring == ring_index else 2
                            cv2.circle(display_frame, center, ring_radius, ring_color, line_thickness)

                            # 绘制检测到的红色点
                            point_limit = 100 if show_specific_ring == ring_index else 30
                            for point in detected_points[:point_limit]:
                                cv2.circle(display_frame, point, 1, ring_color, -1)

                # 显示红环信息
                y_offset = 60
                for ring in all_rings_info['rings']:
                    confidence = ring.get('confidence', 0.0)
                    detected_count = len(ring.get('detected_points', []))
                    ring_color = ring['color']

                    if confidence > 0.02:
                        status = "HIGH" if confidence > 0.3 else "MED" if confidence > 0.1 else "LOW"
                        text = f"Ring {ring['index']}: {confidence:.3f} ({detected_count}px) [{status}]"
                        cv2.putText(display_frame, text, (10, y_offset),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, ring_color, 1)
                        y_offset += 20

            # 显示控制信息
            ring_status = "ON" if show_rings else "OFF"
            ring_color = (0, 255, 0) if show_rings else (0, 0, 255)

            if show_specific_ring == 0:
                display_text = f"All Rings: {ring_status} (Press 't')"
            else:
                display_text = f"Ring {show_specific_ring}: {ring_status} (Press 't')"

            cv2.putText(display_frame, display_text,
                       (10, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.6, ring_color, 2)

            cv2.putText(display_frame, "Press 1-5: Show specific ring, 0: Show all",
                       (10, 230), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            # 显示检测统计
            if detection_stats['total_frames'] > 0:
                black_rate = detection_stats['black_detected'] / detection_stats['total_frames'] * 100
                cv2.putText(display_frame, f"Black detection: {black_rate:.1f}%",
                           (10, 260), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                # 显示各个红环的检测率
                y_pos = 280
                for i in range(5):
                    ring_rate = detection_stats['rings_detected'][i] / detection_stats['total_frames'] * 100
                    conf_rate = detection_stats['high_confidence'][i] / detection_stats['total_frames'] * 100

                    color = (0, 0, 255) if i == 2 else (255, 255, 255)  # 第三环用红色
                    text = f"R{i+1}: {ring_rate:.1f}% (HC:{conf_rate:.1f}%)"
                    cv2.putText(display_frame, text, (10, y_pos),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
                    y_pos += 15

            cv2.imshow("Third Ring Detection Test", display_frame)

            # 键盘控制
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                filename = f"five_rings_test_{int(cv2.getTickCount())}.jpg"
                cv2.imwrite(filename, display_frame)
                print(f"截图保存: {filename}")
            elif key == ord('t'):
                show_rings = not show_rings
                status = "开启" if show_rings else "关闭"
                print(f"红环显示已{status}")
            elif key == ord('r'):
                # 重置统计
                detection_stats = {
                    'total_frames': 0,
                    'black_detected': 0,
                    'rings_detected': [0, 0, 0, 0, 0],
                    'high_confidence': [0, 0, 0, 0, 0]
                }
                print("统计数据已重置")
            elif key in [ord('0'), ord('1'), ord('2'), ord('3'), ord('4'), ord('5')]:
                show_specific_ring = int(chr(key))
                if show_specific_ring == 0:
                    print("显示所有红环")
                else:
                    print(f"只显示第{show_specific_ring}个红环")

    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中出错: {e}")
    finally:
        # 清理资源
        if vision.cap:
            vision.cap.release()
        cv2.destroyAllWindows()
        
        # 显示最终统计
        print("\n" + "=" * 50)
        print("五个红环检测测试统计结果:")
        print("=" * 50)
        if detection_stats['total_frames'] > 0:
            print(f"总帧数: {detection_stats['total_frames']}")
            print(f"黑框检测率: {detection_stats['black_detected']/detection_stats['total_frames']*100:.1f}%")
            print("\n各红环检测统计:")
            print("-" * 30)
            for i in range(5):
                ring_rate = detection_stats['rings_detected'][i] / detection_stats['total_frames'] * 100
                conf_rate = detection_stats['high_confidence'][i] / detection_stats['total_frames'] * 100
                ring_name = f"第{i+1}环 ({2*(i+1)}cm)" + (" [第三环-高亮]" if i == 2 else "")
                print(f"{ring_name}: 检测率 {ring_rate:.1f}%, 高置信度 {conf_rate:.1f}%")
        print("=" * 50)
        print("测试完成")

    return True

if __name__ == "__main__":
    test_third_ring_detection()
