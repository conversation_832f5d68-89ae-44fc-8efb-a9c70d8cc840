#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三红环检测测试脚本
用于验证基于黑框预估第三红环位置的功能
"""

import cv2
import numpy as np
import sys
import os

# 添加当前目录到Python路径，以便导入主模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主模块中的视觉系统类
try:
    from 树莓派opencv识别代码 import RaspberryPiVisionSystem
    print("成功导入视觉系统模块")
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保'树莓派opencv识别代码.py'文件在当前目录中")
    sys.exit(1)

def test_third_ring_detection():
    """测试第三红环检测功能"""
    print("=" * 60)
    print("第三红环检测测试")
    print("=" * 60)
    print("功能说明:")
    print("1. 基于精确识别的黑框预估第三红环位置")
    print("2. 根据题目尺寸规格计算像素/厘米比例")
    print("3. 在预估区域检测红色像素")
    print("4. 显示检测结果和置信度")
    print("")
    print("控制说明:")
    print("- 'q': 退出测试")
    print("- 's': 截图保存")
    print("- 't': 切换第三红环显示")
    print("- 'r': 重置参数")
    print("=" * 60)

    # 初始化视觉系统
    try:
        vision = RaspberryPiVisionSystem(camera_id=0)
        print("摄像头初始化成功")
    except Exception as e:
        print(f"摄像头初始化失败: {e}")
        return False

    show_third_ring = True
    frame_count = 0
    detection_stats = {
        'total_frames': 0,
        'black_detected': 0,
        'ring_detected': 0,
        'high_confidence': 0
    }

    try:
        while True:
            ret, frame = vision.cap.read()
            if not ret:
                print("无法读取摄像头画面")
                continue

            frame_count += 1
            detection_stats['total_frames'] += 1

            # 检测目标（包含第三红环信息）
            result = vision.detect_target_with_bbox(frame)
            if len(result) == 5:
                target_x, target_y, black_bbox, red_bbox, third_ring_info = result
            else:
                target_x, target_y, black_bbox, red_bbox = result
                third_ring_info = None

            # 创建显示画面
            display_frame = frame.copy()

            # 绘制画面中心
            cv2.circle(display_frame, (vision.frame_center_x, vision.frame_center_y),
                      5, (0, 255, 255), -1)
            cv2.putText(display_frame, "CENTER",
                       (vision.frame_center_x + 10, vision.frame_center_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

            # 统计更新
            if target_x is not None:
                detection_stats['black_detected'] += 1

            # 绘制黑框检测结果
            if target_x is not None and black_bbox is not None:
                try:
                    if len(black_bbox) == 5:  # 包含轮廓信息
                        x1, y1, x2, y2, contour = black_bbox
                        if contour is not None:
                            cv2.drawContours(display_frame, [contour], -1, (0, 255, 0), 2)
                        cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 1)
                        cv2.putText(display_frame, "BLACK FRAME",
                                   (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                    else:
                        x1, y1, x2, y2 = black_bbox[:4]
                        cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                        cv2.putText(display_frame, "BLACK FRAME",
                                   (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                except Exception as e:
                    print(f"绘制黑框时出错: {e}")

            # 绘制红色靶心
            if red_bbox is not None:
                cv2.circle(display_frame, (target_x, target_y), 15, (0, 0, 255), 2)
                cv2.circle(display_frame, (target_x, target_y), 3, (0, 0, 255), -1)
                cv2.putText(display_frame, "RED TARGET",
                           (target_x + 20, target_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

            # 绘制第三红环预估结果
            if show_third_ring and third_ring_info is not None:
                detection_stats['ring_detected'] += 1
                
                ring_center = third_ring_info['center']
                ring_radius = third_ring_info['radius']
                detected_points = third_ring_info['detected_points']
                confidence = third_ring_info['confidence']
                
                if confidence > 0.3:
                    detection_stats['high_confidence'] += 1
                
                # 绘制预估的红环圆圈（蓝色）
                cv2.circle(display_frame, ring_center, ring_radius, (255, 0, 0), 3)
                
                # 绘制内外边界（浅蓝色虚线效果）
                inner_radius = int(5 * ring_radius / 6)
                outer_radius = int(7 * ring_radius / 6)
                
                # 绘制虚线圆圈
                for angle in range(0, 360, 10):
                    x1 = int(ring_center[0] + inner_radius * np.cos(np.radians(angle)))
                    y1 = int(ring_center[1] + inner_radius * np.sin(np.radians(angle)))
                    x2 = int(ring_center[0] + outer_radius * np.cos(np.radians(angle)))
                    y2 = int(ring_center[1] + outer_radius * np.sin(np.radians(angle)))
                    cv2.line(display_frame, (x1, y1), (x2, y2), (255, 100, 100), 1)
                
                # 绘制检测到的红色点（黄色小点）
                point_count = min(100, len(detected_points))
                for i, point in enumerate(detected_points[:point_count]):
                    # 根据点的位置调整颜色深度
                    intensity = int(255 * (1 - i / point_count))
                    cv2.circle(display_frame, point, 2, (0, intensity, 255), -1)
                
                # 显示置信度信息
                confidence_text = f"3rd Ring Confidence: {confidence:.3f}"
                confidence_color = (0, 255, 0) if confidence > 0.5 else (0, 255, 255) if confidence > 0.2 else (0, 0, 255)
                cv2.putText(display_frame, confidence_text,
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, confidence_color, 2)
                
                # 显示检测统计
                cv2.putText(display_frame, f"Red pixels: {len(detected_points)}",
                           (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                cv2.putText(display_frame, f"Ring radius: {ring_radius}px",
                           (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # 显示控制信息
            ring_status = "ON" if show_third_ring else "OFF"
            ring_color = (0, 255, 0) if show_third_ring else (0, 0, 255)
            cv2.putText(display_frame, f"3rd Ring Display: {ring_status} (Press 't')",
                       (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.6, ring_color, 2)

            # 显示检测统计
            if detection_stats['total_frames'] > 0:
                black_rate = detection_stats['black_detected'] / detection_stats['total_frames'] * 100
                ring_rate = detection_stats['ring_detected'] / detection_stats['total_frames'] * 100
                confidence_rate = detection_stats['high_confidence'] / detection_stats['total_frames'] * 100
                
                cv2.putText(display_frame, f"Black detection: {black_rate:.1f}%",
                           (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                cv2.putText(display_frame, f"Ring detection: {ring_rate:.1f}%",
                           (10, 170), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
                cv2.putText(display_frame, f"High confidence: {confidence_rate:.1f}%",
                           (10, 190), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

            cv2.imshow("Third Ring Detection Test", display_frame)

            # 键盘控制
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                filename = f"third_ring_test_{int(cv2.getTickCount())}.jpg"
                cv2.imwrite(filename, display_frame)
                print(f"截图保存: {filename}")
            elif key == ord('t'):
                show_third_ring = not show_third_ring
                status = "开启" if show_third_ring else "关闭"
                print(f"第三红环显示已{status}")
            elif key == ord('r'):
                # 重置统计
                detection_stats = {
                    'total_frames': 0,
                    'black_detected': 0,
                    'ring_detected': 0,
                    'high_confidence': 0
                }
                print("统计数据已重置")

    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中出错: {e}")
    finally:
        # 清理资源
        if vision.cap:
            vision.cap.release()
        cv2.destroyAllWindows()
        
        # 显示最终统计
        print("\n" + "=" * 40)
        print("测试统计结果:")
        print("=" * 40)
        if detection_stats['total_frames'] > 0:
            print(f"总帧数: {detection_stats['total_frames']}")
            print(f"黑框检测率: {detection_stats['black_detected']/detection_stats['total_frames']*100:.1f}%")
            print(f"红环检测率: {detection_stats['ring_detected']/detection_stats['total_frames']*100:.1f}%")
            print(f"高置信度率: {detection_stats['high_confidence']/detection_stats['total_frames']*100:.1f}%")
        print("测试完成")

    return True

if __name__ == "__main__":
    test_third_ring_detection()
