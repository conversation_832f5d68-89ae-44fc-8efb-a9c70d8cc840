# QVGA黑框识别包装函数演示
# 保持原有Flag不变，只是把QVGA识别包装成函数在任务中调用

import time
import os
import math
import gc
from time import ticks_ms
from machine import FPIOA, Pin, UART, TOUCH
from media.sensor import *
from media.display import *
from media.media import *

#______________________________________参数配置___________________________________________

# 保持原有的Flag设置
Flag = 2  # 2:基础部分第二题, 3:基础部分第三题, 4:发挥部分第一题, 5:发挥部分第二题

# 分辨率状态
current_resolution = "HD"

# 检测阈值
black_threshold = (0, 30, -20, 20, -20, 20)
blue_threshold = (0, 100, -127, -10, -127, 127)
binary_threshold = 128

# 全局变量
rect_cx = 400
rect_cy = 240

#_________________________________________模块初始化_____________________________________________

# 摄像头初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width=800, height=480)
sensor.set_pixformat(Sensor.RGB565)

# 串口初始化
fpioa = FPIOA()
fpioa.set_function(3, FPIOA.UART1_TXD)
fpioa.set_function(4, FPIOA.UART1_RXD)
uart = UART(UART.UART1, 115200)

# 显示初始化
Display.init(Display.ST7701, to_ide=True)
MediaManager.init()
sensor.run()
clock = time.clock()

print("QVGA黑框识别包装函数演示系统初始化完成")

#__________________________________________分辨率切换函数_____________________________________________

def sensor_QVGA():
    """切换到QVGA分辨率"""
    global current_resolution
    
    if current_resolution == "QVGA":
        return True
    
    try:
        sensor.stop()
        time.sleep_ms(30)
        
        sensor.reset()
        sensor.set_framesize(width=320, height=240)
        sensor.set_pixformat(Sensor.RGB565)
        
        sensor.run()
        time.sleep_ms(50)
        
        current_resolution = "QVGA"
        return True
        
    except Exception as e:
        print(f"QVGA切换失败: {e}")
        return False

def sensor_HD():
    """切换到HD分辨率"""
    global current_resolution
    
    if current_resolution == "HD":
        return True
    
    try:
        sensor.stop()
        time.sleep_ms(30)
        
        sensor.reset()
        sensor.set_framesize(width=800, height=480)
        sensor.set_pixformat(Sensor.RGB565)
        
        sensor.run()
        time.sleep_ms(50)
        
        current_resolution = "HD"
        return True
        
    except Exception as e:
        print(f"HD切换失败: {e}")
        return False

#__________________________________________功能函数_____________________________________________

def safe_uart_send(data_list):
    """安全的串口发送"""
    try:
        uart.write(bytes(data_list))
        return True
    except:
        return False

def calculate_distance(x1, y1, x2, y2):
    """计算两点间距离"""
    return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)

def img_bin_rects(img):
    """二值化并查找矩形"""
    try:
        img_gray = img.to_grayscale()
        img_bin = img_gray.binary([(0, binary_threshold)])
        rects = img_bin.find_rects(threshold=1000)
        return rects
    except:
        return []

def find_max_rect(rects):
    """找到最大的矩形"""
    if not rects:
        return None
    return max(rects, key=lambda r: r.area())

def find_max_blob(blobs):
    """找到最大的色块"""
    if not blobs:
        return None
    return max(blobs, key=lambda b: b.pixels())

#__________________________________________QVGA黑框识别包装函数_____________________________________________

def detect_black_frame_QVGA():
    """
    QVGA高帧率黑框识别包装函数
    返回: (是否找到目标, 目标中心X, 目标中心Y, HD坐标系误差X, HD坐标系误差Y)
    """
    global rect_cx, rect_cy
    
    # 自动切换到QVGA分辨率
    sensor_QVGA()
    
    img = sensor.snapshot()
    width, height = 320, 240  # QVGA分辨率
    center_x = width // 2
    center_y = height // 2
    
    # 使用二值化方法检测黑框
    rects = img_bin_rects(img)
    
    target_found = False
    e_x_hd = 0
    e_y_hd = 0
    
    if rects:
        max_rect = find_max_rect(rects)
        if max_rect:
            # 计算矩形中心点
            rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
            rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

            # 绘制矩形中心点和轮廓
            img.draw_circle(rect_cx, rect_cy, 3, color=(255, 0, 0), thickness=2, fill=True)

            corners = max_rect.corners()
            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(0, 255, 0), thickness=2)

            # 计算误差（相对于QVGA中心点）
            e_x = center_x - rect_cx
            e_y = center_y - rect_cy
            
            # 将QVGA坐标转换为HD坐标系用于控制
            e_x_hd = int(e_x * 2.5)  # QVGA->HD X轴缩放
            e_y_hd = int(e_y * 2.0)  # QVGA->HD Y轴缩放
            
            distance = calculate_distance(rect_cx, rect_cy, center_x, center_y)
            target_found = True

            # 显示信息
            img.draw_string_advanced(5, 5, 10, f"黑框跟踪 QVGA", color=(0, 255, 0))
            img.draw_string_advanced(5, 16, 8, f"中心: ({rect_cx}, {rect_cy})", color=(255, 255, 255))
            img.draw_string_advanced(5, 27, 8, f"QVGA误差: ({e_x}, {e_y})", color=(255, 255, 255))
            img.draw_string_advanced(5, 38, 8, f"HD误差: ({e_x_hd}, {e_y_hd})", color=(255, 255, 255))
            img.draw_string_advanced(5, 49, 8, f"距离: {distance:.1f}px", color=(255, 255, 255))
    else:
        img.draw_string_advanced(5, 5, 12, "搜索黑框中... QVGA", color=(255, 255, 0))
    
    # 显示帧率信息
    fps = clock.fps()
    img.draw_string_advanced(5, height-15, 8, f"FPS: {fps:.1f} {width}x{height}", color=(255, 255, 255))
    
    # 绘制中心十字线
    img.draw_cross(center_x, center_y, color=(0, 0, 255), size=6, thickness=1)
    
    Display.show_image(img)
    
    return target_found, rect_cx if target_found else 0, rect_cy if target_found else 0, e_x_hd, e_y_hd

#__________________________________________原有任务函数（保持Flag不变）_____________________________________________

def goto_target_point_1():
    """Flag==2 基础部分第二题 - 可选择使用QVGA黑框识别"""
    global rect_cx, rect_cy
    
    # 选择使用QVGA高帧率黑框识别
    use_qvga_detection = True  # 设置为True使用QVGA，False使用HD
    
    if use_qvga_detection:
        # 使用QVGA黑框识别包装函数
        target_found, target_x, target_y, e_x_hd, e_y_hd = detect_black_frame_QVGA()
        
        if target_found:
            # 发送控制信号（已经转换为HD坐标系）
            e_x_high = (e_x_hd >> 8) & 0xFF
            e_x_low = e_x_hd & 0xFF
            e_y_high = (e_y_hd >> 8) & 0xFF
            e_y_low = e_y_hd & 0xFF

            send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
            safe_uart_send(send_lst)

            print(f"发送控制信号: X={e_x_hd}, Y={e_y_hd}")
        return
    
    # 原有的HD方法（如果需要的话）
    sensor_HD()
    img = sensor.snapshot()
    # ... 原有的HD处理逻辑
    Display.show_image(img)

def goto_target_point_2():
    """Flag==3 基础部分第三题 - 可选择使用QVGA黑框识别"""
    # 与goto_target_point_1类似的逻辑
    goto_target_point_1()  # 复用相同逻辑

def goto_target_point_3():
    """Flag==4,5 发挥部分第一、二题 - 可选择使用QVGA黑框识别"""
    # 与goto_target_point_1类似的逻辑
    goto_target_point_1()  # 复用相同逻辑

#_____________________________________________主程序________________________________________________
while True:
    try:
        clock.tick()
        os.exitpoint()
        
        # 保持原有的Flag逻辑
        if Flag == 2:
            goto_target_point_1()
        elif Flag == 3:
            goto_target_point_2()
        elif Flag == 4 or Flag == 5:
            goto_target_point_3()
        else:
            # 默认显示
            img = sensor.snapshot()
            img.draw_string_advanced(10, 10, 20, f"QVGA黑框识别包装函数演示", color=(255, 255, 255))
            img.draw_string_advanced(10, 35, 16, f"当前Flag={Flag}", color=(255, 255, 255))
            img.draw_string_advanced(10, 55, 16, f"Flag=2: 基础部分第二题", color=(255, 255, 0))
            img.draw_string_advanced(10, 75, 16, f"Flag=3: 基础部分第三题", color=(255, 255, 0))
            img.draw_string_advanced(10, 95, 16, f"Flag=4,5: 发挥部分", color=(255, 255, 0))
            
            fps = clock.fps()
            img.draw_string_advanced(10, 115, 16, f"FPS: {fps:.1f}", color=(255, 255, 255))
            img.draw_string_advanced(10, 135, 16, f"分辨率: {current_resolution}", color=(255, 255, 255))
            
            Display.show_image(img)
        
        # 定期内存清理
        if ticks_ms() % 5000 < 50:
            gc.collect()
            
    except Exception as e:
        print(f"程序错误: {e}")
        time.sleep_ms(100)
