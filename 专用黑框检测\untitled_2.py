# K230黑框识别算法 - 基于OpenMV算法思路转换
# 原算法作者：B站@程欢欢的智能控制集 20250730
# K230转换版本

import time
import os
import sys
import math
import random
import gc

from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import UART
from machine import RTC
from machine import ADC
from machine import TOUCH

from media.sensor import *
from media.display import *
from media.media import *

#______________________________________变量定义并赋初值___________________________________________

# 黑框和白底的颜色阈值 (LAB色彩空间)
thr_white = (60, 100, -20, 20, -20, 20)  # 白色阈值
thr_black = (0, 35, -20, 20, -20, 20)    # 黑色阈值

# 目标跟踪变量
target_offset = [-1, -1]  # 目标补偿量，根据视角倾斜角度设置
target = [0, 0]           # 目标坐标，自动计算
view_offset = [0, 0]      # 视角偏移补偿量，自动计算
deviation = [0, 0]        # 偏差量，最终输出结果

# 画面中心坐标
frame_center_x = 200
frame_center_y = 120

# 矩形四点的坐标
rect_corner_points = []

# K230版本的颜色阈值转换函数
def lab_to_rgb_threshold(lab_threshold):
    """将LAB阈值转换为RGB阈值（简化版本）"""
    # 这是一个简化的转换，实际使用中可能需要调整
    l_min, l_max, a_min, a_max, b_min, b_max = lab_threshold

    # 对于黑色：L值低，RGB值也低
    if l_max < 50:  # 黑色
        return (0, 50)  # 灰度阈值
    else:  # 白色或其他颜色
        return (200, 255)  # 灰度阈值

# 转换颜色阈值
black_threshold = lab_to_rgb_threshold(thr_black)  # 黑色阈值
white_threshold = lab_to_rgb_threshold(thr_white)  # 白色阈值

#_________________________________________模块初始化_____________________________________________

#摄像头传感器初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width = 400, height = 240)
sensor.set_pixformat(Sensor.RGB565)

#串口初始化
fpioa = FPIOA()
# UART1代码
fpioa.set_function(3,FPIOA.UART1_TXD)
fpioa.set_function(4,FPIOA.UART1_RXD)

uart=UART(UART.UART1,115200) #设置串口号1和波特率

fpioa.set_function(52,FPIOA.GPIO52)
LED=Pin(52,Pin.OUT) #构建led对象，GPIO52,输出

#缓冲区和3.5寸的屏幕图像显示
Display.init(Display.ST7701, to_ide=True)

# 初始化媒体管理器
MediaManager.init()

#启动摄像头传感器模块
sensor.run()

#时钟模块建立
clock = time.clock()

#建立rtc计时器
#rtc = machine.RTC()

#建立触摸屏
tp = TOUCH(0)

#_________________________________________原有功能函数定义_____________________________________________

#找到最大矩形
def find_max_rect(rectangles):
    max_size = 0
    max_rect = None
    for rect in rectangles:
        if rect.w()*rect.h() > max_size :
            max_rect = rect
            max_size = rect.w()*rect.h()
    return max_rect

#拟合矩形 - 卡尔曼滤波稳定化
def detect_and_draw_rectangles():
    global rect_corner_points, last_cx, last_cy, first_frame
    img = sensor.snapshot()
    # 转换到灰度图像进行矩形检测
    img_gray = img.to_grayscale(copy=True)
    img.bilateral(1, 30, 30)  # 双边滤波
    # 应用二值化增强边缘
    img_binary = img_gray.binary([binary_threshold])
    rects = img_binary.find_rects(threshold=5000)
    # 清空前一次的角点
    rect_corner_points = []

    if rects:
        max_rect = find_max_rect(rects)
        if max_rect:
            # 原始中心点
            raw_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
            raw_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

            # 计算速度
            if first_frame:
                vx = 0
                vy = 0
                first_frame = False
            else:
                vx = raw_cx - last_cx
                vy = raw_cy - last_cy

            # 使用卡尔曼滤波
            filtered_cx, pred_vx = Kalman_x_filter(raw_cx, vx)
            filtered_cy, pred_vy = Kalman_y_filter(raw_cy, vy)

            # 转换为整数
            rect_cx = int(filtered_cx)
            rect_cy = int(filtered_cy)

            # 更新上一帧位置
            last_cx = raw_cx
            last_cy = raw_cy

            corners = max_rect.corners()
            # 绘制矩形中心点和轮廓
            img.draw_circle(rect_cx, rect_cy, 2, color=(255, 0, 0), thickness=2, fill=True)
            img.draw_rectangle(max_rect.rect(), color=(0, 255, 0), thickness=3)
            img.draw_cross(rect_cx, rect_cy, color=(255, 255, 0), size=10, thickness=2)

            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(255, 0, 0), thickness=2)
    return img

while True:
    clock.tick()
    os.exitpoint()
    img = detect_and_draw_rectangles()
    Display.show_image(img)
