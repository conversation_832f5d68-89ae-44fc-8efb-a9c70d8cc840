import time
import os
import sys
import math
import random
import gc

from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import UART
from machine import RTC
from machine import ADC
from machine import TOUCH

from media.sensor import *
from media.display import *
from media.media import *

#______________________________________变量定义并赋初值___________________________________________
#(0,0,0,0,0,0)
#矩形四点的坐标
rect_corner_points = []

binary_threshold = (70,192)

#矩形变换函数，用于kalerman
class Vector:
    def __init__(self, lis):
        self._values = lis

    @classmethod
    def zero(cls, dim):
        return cls([0] * dim)

    def norm(self):
        return math.sqrt(sum(e**2 for e in self))

    def normalize(self):
        if self.norm() < 1e-8:
            raise ZeroDivisionError("Normalize error! norm is zero.")
        return 1 / self.norm() * Vector(self._values)

    def __add__(self, other):
        assert len(self) == len(other), "Vector length must be same"
        return Vector([a + b for a, b in zip(self, other)])

    def __sub__(self, other):
        assert len(self) == len(other), "Vector length must be same"
        return Vector([a - b for a, b in zip(self, other)])

    def dot(self, other):
        assert len(self) == len(other), "Vector length must be same."
        return sum(a * b for a,b in zip(self, other))

    def __mul__(self, k):
        return Vector([a * k for a in self])

    def __rmul__(self, k):
        return self * k

    def __truediv__(self, k):
        return 1/k * self

    def __pos__(self):
        return 1*self

    def __neg__(self):
        return -1*self

    def __getitem__(self, index):
        return self._values[index]

    def __len__(self):
        return len(self._values)

    def __repr__(self):
        return "Vector({})".format(self._values)

    def __str__(self):
        return "({})".format(",".join(str(e) for e in self._values))

class Matrix:
    def __init__(self, list2d):
        self._values = [row[:] for row in list2d]

    @classmethod
    def zero(cls, r, c):
        return cls([[0] * c for _ in range(r)])

    def T(self):
        return Matrix([[e for e in self.col_vector(i)]
                      for i in range(self.col_num())])

    def __add__(self, another):
        assert self.shape() == another.shape(), "Matrix shape must be same."
        return Matrix([[a + b for a, b in zip(self.row_vector(i), another.row_vector(i))]
                      for i in range(self.row_num())])

    def __sub__(self, another):
        assert self.shape() == another.shape(), "Matrix shape must be same."
        return Matrix([[a - b for a, b in zip(self.row_vector(i), another.row_vector(i))]
                      for i in range(self.row_num())])

    def dot(self, another):
        if isinstance(another, Vector):
            assert self.col_num() == len(another), "Error in Matrix-Vector Multiplication."
            return Vector([self.row_vector(i).dot(another) for i in range(self.row_num())])

        if isinstance(another, Matrix):
            assert self.col_num() == another.row_num(), "Error in Matrix-Matrix Multiplication."
            return Matrix([[self.row_vector(i).dot(another.col_vector(j)) for j in range(another.col_num())]
                          for i in range(self.row_num())])

    def __mul__(self, k):
        return Matrix([[e * k for e in self.row_vector(i)]
                      for i in range(self.row_num())])

    def __rmul__(self, k):
        return self * k

    def __truediv__(self, k):
        return (1 / k) * self

    def __pos__(self):
        return 1 * self

    def __neg__(self):
        return -1 * self

    def row_vector(self, index):
        return Vector(self._values[index])

    def col_vector(self, index):
        return Vector([row[index] for row in self._values])

    def __getitem__(self, pos):
        r, c = pos
        return self._values[r][c]

    def ni_matrix(self):
        """返回矩阵的逆（二维矩阵）"""
        a = self._values[0][0]
        b = self._values[0][1]
        c = self._values[1][0]
        d = self._values[1][1]
        det = a * d - b * c
        if abs(det) < 1e-10:  # 防止行列式接近零
            return Matrix([[1, 0], [0, 1]])
        ni_mat = Matrix([[d, -b], [-c, a]])
        return (1/det) * ni_mat

    def row_num(self):
        return len(self._values)

    def col_num(self):
        return len(self._values[0])

    def shape(self):
        return self.row_num(), self.col_num()

    def __repr__(self):
        return "Matrix({})".format(self._values)

#________________________________________卡尔曼滤波器模型___________________________________________

# 状态转移矩阵 (假设匀速运动模型)
A = Matrix([[1, 1], [0, 1]])  # [位置, 速度]

# 噪声协方差矩阵
R = Matrix([[0.1, 0], [0, 0.1]])  # 测量噪声协方差
Q = Matrix([[0.1, 0], [0, 0.1]])  # 过程噪声协方差

# 滤波器状态变量
Xk_1hat = Matrix([[0], [0]])  # X方向状态
Yk_1hat = Matrix([[0], [0]])  # Y方向状态
XPk_1 = Matrix([[1, 0], [0, 1]])  # X方向误差协方差
YPk_1 = Matrix([[1, 0], [0, 1]])  # Y方向误差协方差

# 常数矩阵
I = Matrix([[1, 0], [0, 1]])  # 单位矩阵
H = Matrix([[1, 0], [0, 1]])  # 观测矩阵

# 上一帧的位置，用于计算速度
last_cx = 0
last_cy = 0
first_frame = True

#kalerman滤波函数定义
def Kalman_x_filter(cx, vx):
    global A, I, H, Q, R, Xk_1hat, XPk_1

    # 预测步骤
    pre_Xkhat = A.dot(Xk_1hat)
    pre_Pk = (A.dot(XPk_1)).dot(A.T()) + Q

    # 卡尔曼增益
    Kk = pre_Pk.dot((pre_Pk + R).ni_matrix())

    # 更新步骤
    Zk = Matrix([[cx], [vx]])
    Xk_hat = pre_Xkhat + Kk.dot(Zk - H.dot(pre_Xkhat))
    Pk_1 = (I - Kk.dot(H)).dot(pre_Pk)

    # 更新状态
    XPk_1 = Pk_1
    Xk_1hat = Xk_hat

    return Xk_hat[0,0], Xk_hat[1,0]

def Kalman_y_filter(cy, vy):
    global A, I, H, Q, R, Yk_1hat, YPk_1

    # 预测步骤
    pre_Ykhat = A.dot(Yk_1hat)
    pre_Pk = (A.dot(YPk_1)).dot(A.T()) + Q

    # 卡尔曼增益
    Kk = pre_Pk.dot((pre_Pk + R).ni_matrix())

    # 更新步骤
    Zk = Matrix([[cy], [vy]])
    Yk_hat = pre_Ykhat + Kk.dot(Zk - H.dot(pre_Ykhat))
    Pk_1 = (I - Kk.dot(H)).dot(pre_Pk)

    # 更新状态
    YPk_1 = Pk_1
    Yk_1hat = Yk_hat

    return Yk_hat[0,0], Yk_hat[1,0]

#_________________________________________模块初始化_____________________________________________

#摄像头传感器初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width = 400, height = 240)
sensor.set_pixformat(Sensor.RGB565)

#串口初始化
fpioa = FPIOA()
# UART1代码
fpioa.set_function(3,FPIOA.UART1_TXD)
fpioa.set_function(4,FPIOA.UART1_RXD)

uart=UART(UART.UART1,115200) #设置串口号1和波特率

fpioa.set_function(52,FPIOA.GPIO52)
LED=Pin(52,Pin.OUT) #构建led对象，GPIO52,输出

#缓冲区和3.5寸的屏幕图像显示
Display.init(Display.ST7701, to_ide=True)

# 初始化媒体管理器
MediaManager.init()

#启动摄像头传感器模块
sensor.run()

#时钟模块建立
clock = time.clock()

#建立rtc计时器
#rtc = machine.RTC()

#建立触摸屏
tp = TOUCH(0)

#_________________________________________原有功能函数定义_____________________________________________

#找到最大矩形
def find_max_rect(rectangles):
    max_size = 0
    max_rect = None
    for rect in rectangles:
        if rect.w()*rect.h() > max_size :
            max_rect = rect
            max_size = rect.w()*rect.h()
    return max_rect

#拟合矩形 - 卡尔曼滤波稳定化
def detect_and_draw_rectangles():
    global rect_corner_points, last_cx, last_cy, first_frame
    img = sensor.snapshot()
    # 转换到灰度图像进行矩形检测
    img_gray = img.to_grayscale(copy=True)
    img.bilateral(1, 30, 30)  # 双边滤波
    # 应用二值化增强边缘
    img_binary = img_gray.binary([binary_threshold])
    rects = img_binary.find_rects(threshold=5000)
    # 清空前一次的角点
    rect_corner_points = []

    if rects:
        max_rect = find_max_rect(rects)
        if max_rect:
            # 原始中心点
            raw_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
            raw_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

            # 计算速度
            if first_frame:
                vx = 0
                vy = 0
                first_frame = False
            else:
                vx = raw_cx - last_cx
                vy = raw_cy - last_cy

            # 使用卡尔曼滤波
            filtered_cx, pred_vx = Kalman_x_filter(raw_cx, vx)
            filtered_cy, pred_vy = Kalman_y_filter(raw_cy, vy)

            # 转换为整数
            rect_cx = int(filtered_cx)
            rect_cy = int(filtered_cy)

            # 更新上一帧位置
            last_cx = raw_cx
            last_cy = raw_cy

            corners = max_rect.corners()
            # 绘制矩形中心点和轮廓
            img.draw_circle(rect_cx, rect_cy, 2, color=(255, 0, 0), thickness=2, fill=True)
            img.draw_rectangle(max_rect.rect(), color=(0, 255, 0), thickness=3)
            img.draw_cross(rect_cx, rect_cy, color=(255, 255, 0), size=10, thickness=2)

            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(255, 0, 0), thickness=2)
    return img

while True:
    clock.tick()
    os.exitpoint()
    img = detect_and_draw_rectangles()
    Display.show_image(img)
