import time
import os
import sys
import math
import random
import gc

from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import UART
from machine import RTC
from machine import ADC
from machine import TOUCH

from media.sensor import *
from media.display import *
from media.media import *

#______________________________________变量定义并赋初值___________________________________________
#(0,0,0,0,0,0)
#矩形四点的坐标
rect_corner_points = []

binary_threshold = (70,192)

# 卡尔曼滤波器类
class KalmanFilter:
    def __init__(self, process_variance=1e-3, measurement_variance=1e-1):
        self.process_variance = process_variance  # 过程噪声方差
        self.measurement_variance = measurement_variance  # 测量噪声方差
        self.posteri_estimate = 0.0  # 后验估计
        self.posteri_error_estimate = 1.0  # 后验误差估计

    def update(self, measurement):
        # 预测步骤
        priori_estimate = self.posteri_estimate
        priori_error_estimate = self.posteri_error_estimate + self.process_variance

        # 更新步骤
        blending_factor = priori_error_estimate / (priori_error_estimate + self.measurement_variance)
        self.posteri_estimate = priori_estimate + blending_factor * (measurement - priori_estimate)
        self.posteri_error_estimate = (1 - blending_factor) * priori_error_estimate

        return self.posteri_estimate

# 矩形跟踪器类
class RectangleTracker:
    def __init__(self):
        # 为矩形中心点创建卡尔曼滤波器
        self.kf_cx = KalmanFilter(process_variance=1e-3, measurement_variance=5e-1)
        self.kf_cy = KalmanFilter(process_variance=1e-3, measurement_variance=5e-1)

        # 为矩形尺寸创建卡尔曼滤波器
        self.kf_width = KalmanFilter(process_variance=1e-4, measurement_variance=2e-1)
        self.kf_height = KalmanFilter(process_variance=1e-4, measurement_variance=2e-1)

        # 历史数据用于稳定性检查
        self.history_cx = []
        self.history_cy = []
        self.history_width = []
        self.history_height = []
        self.max_history = 5

        # 稳定性参数
        self.stable_threshold = 10  # 位置变化阈值
        self.size_change_threshold = 0.3  # 尺寸变化阈值（比例）

        # 上一帧的有效检测结果
        self.last_valid_rect = None
        self.frames_without_detection = 0
        self.max_frames_without_detection = 5

    def add_to_history(self, cx, cy, width, height):
        """添加数据到历史记录"""
        self.history_cx.append(cx)
        self.history_cy.append(cy)
        self.history_width.append(width)
        self.history_height.append(height)

        # 保持历史记录长度
        if len(self.history_cx) > self.max_history:
            self.history_cx.pop(0)
            self.history_cy.pop(0)
            self.history_width.pop(0)
            self.history_height.pop(0)

    def is_detection_valid(self, rect):
        """检查检测结果是否有效"""
        if not rect:
            return False

        # 检查尺寸是否合理
        if rect.w() < 20 or rect.h() < 20:
            return False

        # 如果有历史数据，检查变化是否过大
        if self.last_valid_rect:
            last_cx = self.last_valid_rect['cx']
            last_cy = self.last_valid_rect['cy']
            last_w = self.last_valid_rect['w']
            last_h = self.last_valid_rect['h']

            current_cx = int(sum(p[0] for p in rect.corners()) / 4)
            current_cy = int(sum(p[1] for p in rect.corners()) / 4)

            # 检查位置变化
            pos_change = math.sqrt((current_cx - last_cx)**2 + (current_cy - last_cy)**2)
            if pos_change > self.stable_threshold * 3:  # 允许较大的位置变化
                return False

            # 检查尺寸变化
            size_change_w = abs(rect.w() - last_w) / last_w
            size_change_h = abs(rect.h() - last_h) / last_h
            if size_change_w > self.size_change_threshold or size_change_h > self.size_change_threshold:
                return False

        return True

    def update(self, rect):
        """更新跟踪器"""
        if rect and self.is_detection_valid(rect):
            # 有效检测
            self.frames_without_detection = 0

            # 计算矩形中心和尺寸
            raw_cx = int(sum(p[0] for p in rect.corners()) / 4)
            raw_cy = int(sum(p[1] for p in rect.corners()) / 4)
            raw_width = rect.w()
            raw_height = rect.h()

            # 使用卡尔曼滤波器平滑数据
            filtered_cx = self.kf_cx.update(raw_cx)
            filtered_cy = self.kf_cy.update(raw_cy)
            filtered_width = self.kf_width.update(raw_width)
            filtered_height = self.kf_height.update(raw_height)

            # 添加到历史记录
            self.add_to_history(filtered_cx, filtered_cy, filtered_width, filtered_height)

            # 更新最后有效检测结果
            self.last_valid_rect = {
                'cx': int(filtered_cx),
                'cy': int(filtered_cy),
                'w': int(filtered_width),
                'h': int(filtered_height),
                'corners': rect.corners()
            }

            return self.last_valid_rect

        else:
            # 无效或无检测
            self.frames_without_detection += 1

            # 如果连续多帧无检测，返回None
            if self.frames_without_detection > self.max_frames_without_detection:
                return None

            # 否则返回上一帧的结果
            return self.last_valid_rect

    def get_stability_score(self):
        """获取稳定性评分 (0-100)"""
        if len(self.history_cx) < 3:
            return 0

        # 计算位置变化的标准差
        cx_std = self.calculate_std(self.history_cx)
        cy_std = self.calculate_std(self.history_cy)

        # 位置稳定性评分
        pos_stability = max(0, 100 - (cx_std + cy_std) * 2)

        return min(100, max(0, pos_stability))

    def calculate_std(self, data):
        """计算标准差"""
        if len(data) < 2:
            return 0
        mean = sum(data) / len(data)
        variance = sum((x - mean) ** 2 for x in data) / len(data)
        return math.sqrt(variance)

# 创建矩形跟踪器实例
rect_tracker = RectangleTracker()

#_________________________________________模块初始化_____________________________________________

#摄像头传感器初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width = 400, height = 240)
sensor.set_pixformat(Sensor.RGB565)

#串口初始化
fpioa = FPIOA()
# UART1代码
fpioa.set_function(3,FPIOA.UART1_TXD)
fpioa.set_function(4,FPIOA.UART1_RXD)

uart=UART(UART.UART1,115200) #设置串口号1和波特率

fpioa.set_function(52,FPIOA.GPIO52)
LED=Pin(52,Pin.OUT) #构建led对象，GPIO52,输出

#缓冲区和3.5寸的屏幕图像显示
Display.init(Display.ST7701, to_ide=True)

# 初始化媒体管理器
MediaManager.init()

#启动摄像头传感器模块
sensor.run()

#时钟模块建立
clock = time.clock()

#建立rtc计时器
#rtc = machine.RTC()

#建立触摸屏
tp = TOUCH(0)

#_________________________________________原有功能函数定义_____________________________________________

#找到最大矩形
def find_max_rect(rectangles):
    max_size = 0
    max_rect = None
    for rect in rectangles:
        if rect.w()*rect.h() > max_size :
            max_rect = rect
            max_size = rect.w()*rect.h()
    return max_rect

#拟合矩形 - 使用保存的灰度阈值和卡尔曼滤波稳定化
def detect_and_draw_rectangles():
    global rect_corner_points, rect_tracker
    img = sensor.snapshot()

    # 转换到灰度图像进行矩形检测
    img_gray = img.to_grayscale(copy=True)
    img.bilateral(1, 30, 30)  # 双边滤波，空间域、颜色域的标准差分别为 5、75

    # 应用二值化增强边缘
    img_binary = img_gray.binary([binary_threshold])
    rects = img_binary.find_rects(threshold=5000)

    # 清空前一次的角点
    rect_corner_points = []

    # 找到最大矩形
    max_rect = None
    if rects:
        max_rect = find_max_rect(rects)

    # 使用跟踪器更新和稳定化检测结果
    tracked_rect = rect_tracker.update(max_rect)

    if tracked_rect:
        rect_cx = tracked_rect['cx']
        rect_cy = tracked_rect['cy']
        rect_w = tracked_rect['w']
        rect_h = tracked_rect['h']

        # 计算矩形的四个角点（基于中心点和尺寸）
        half_w = rect_w // 2
        half_h = rect_h // 2
        corners = [
            (rect_cx - half_w, rect_cy - half_h),  # 左上
            (rect_cx + half_w, rect_cy - half_h),  # 右上
            (rect_cx + half_w, rect_cy + half_h),  # 右下
            (rect_cx - half_w, rect_cy + half_h)   # 左下
        ]

        # 保存角点信息
        rect_corner_points = corners

        # 绘制稳定化后的矩形
        # 绘制矩形框
        img.draw_rectangle((rect_cx - half_w, rect_cy - half_h, rect_w, rect_h),
                          color=(0, 255, 0), thickness=3)

        # 绘制中心点
        img.draw_circle(rect_cx, rect_cy, 3, color=(255, 0, 0), thickness=2, fill=True)
        img.draw_cross(rect_cx, rect_cy, color=(255, 255, 0), size=10, thickness=2)

        # 绘制角点连线
        for i in range(4):
            start_idx = i
            end_idx = (i + 1) % 4
            img.draw_line(corners[start_idx][0], corners[start_idx][1],
                         corners[end_idx][0], corners[end_idx][1],
                         color=(255, 0, 0), thickness=2)

        # 绘制稳定性信息
        stability_score = rect_tracker.get_stability_score()
        img.draw_string(10, 10, f"Stability: {stability_score:.1f}%",
                       color=(255, 255, 255), scale=2)

        # 绘制跟踪状态
        if max_rect:
            img.draw_string(10, 30, "Status: TRACKING", color=(0, 255, 0), scale=2)
        else:
            img.draw_string(10, 30, "Status: PREDICTING", color=(255, 255, 0), scale=2)

        # 绘制矩形尺寸信息
        img.draw_string(10, 50, f"Size: {rect_w}x{rect_h}",
                       color=(255, 255, 255), scale=2)
    else:
        # 没有检测到矩形
        img.draw_string(10, 10, "No Rectangle Detected", color=(255, 0, 0), scale=2)
        img.draw_string(10, 30, "Status: SEARCHING", color=(255, 0, 0), scale=2)

    return img

# 添加自适应阈值调整功能
def adaptive_threshold_adjustment():
    """根据环境光线自动调整二值化阈值"""
    global binary_threshold

    # 获取当前图像
    img = sensor.snapshot()
    img_gray = img.to_grayscale(copy=True)

    # 计算图像的平均亮度
    histogram = img_gray.get_histogram()
    total_pixels = img_gray.width() * img_gray.height()

    # 计算加权平均亮度
    weighted_sum = 0
    for i in range(len(histogram.bins())):
        weighted_sum += i * histogram.bins()[i]

    avg_brightness = weighted_sum / total_pixels

    # 根据平均亮度调整阈值
    if avg_brightness < 80:  # 较暗环境
        binary_threshold = (50, 150)
    elif avg_brightness > 150:  # 较亮环境
        binary_threshold = (90, 220)
    else:  # 正常环境
        binary_threshold = (70, 192)

# 添加帧率统计
frame_count = 0
fps_start_time = time.ticks_ms()

def calculate_fps():
    """计算并显示帧率"""
    global frame_count, fps_start_time
    frame_count += 1

    current_time = time.ticks_ms()
    elapsed = time.ticks_diff(current_time, fps_start_time)

    if elapsed >= 1000:  # 每秒更新一次
        fps = frame_count * 1000 / elapsed
        print(f"FPS: {fps:.1f}")
        frame_count = 0
        fps_start_time = current_time
        return fps
    return None

# 初始化变量
last_threshold_adjustment = time.ticks_ms()
threshold_adjustment_interval = 5000  # 每5秒调整一次阈值

# 主循环
while True:
    try:
        clock.tick()
        os.exitpoint()

        # 定期自适应调整阈值
        current_time = time.ticks_ms()
        if time.ticks_diff(current_time, last_threshold_adjustment) > threshold_adjustment_interval:
            adaptive_threshold_adjustment()
            last_threshold_adjustment = current_time

        # 检测并绘制矩形
        img = detect_and_draw_rectangles()

        # 计算并显示FPS
        fps = calculate_fps()
        if fps is not None:
            img.draw_string(img.width() - 100, 10, f"FPS: {fps:.1f}",
                           color=(255, 255, 255), scale=2)

        # 显示当前阈值
        img.draw_string(10, img.height() - 30, f"Threshold: {binary_threshold}",
                       color=(255, 255, 255), scale=2)

        # 显示图像
        Display.show_image(img)

        # 检查触摸屏输入，用于手动调整阈值
        touch_point = tp.read()
        if touch_point:
            x, y = touch_point
            if y < 50:  # 屏幕顶部区域
                if x < img.width() // 2:
                    # 左侧：降低阈值
                    binary_threshold = (max(0, binary_threshold[0] - 5),
                                       max(0, binary_threshold[1] - 5))
                else:
                    # 右侧：提高阈值
                    binary_threshold = (min(255, binary_threshold[0] + 5),
                                       min(255, binary_threshold[1] + 5))
                print(f"手动调整阈值: {binary_threshold}")

    except Exception as e:
        # 错误处理
        print(f"错误: {e}")
        img = sensor.snapshot()
        img.draw_string(10, 10, f"Error: {e}", color=(255, 0, 0), scale=2)
        Display.show_image(img)
        time.sleep(1)
