import time
import os
import sys
import math
import random
import gc

from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import UART
from machine import RTC
from machine import ADC
from machine import TOUCH

from media.sensor import *
from media.display import *
from media.media import *

#______________________________________变量定义并赋初值___________________________________________
#(0,0,0,0,0,0)
#矩形四点的坐标
rect_corner_points = []

binary_threshold = (70,192)

# 简单滑动平均滤波
class SimpleFilter:
    def __init__(self, size=3):
        self.size = size
        self.values = []

    def update(self, value):
        self.values.append(value)
        if len(self.values) > self.size:
            self.values.pop(0)
        return sum(self.values) / len(self.values)

# 简单稳定器
cx_filter = SimpleFilter(3)
cy_filter = SimpleFilter(3)

#_________________________________________模块初始化_____________________________________________

#摄像头传感器初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width = 400, height = 240)
sensor.set_pixformat(Sensor.RGB565)

#串口初始化
fpioa = FPIOA()
# UART1代码
fpioa.set_function(3,FPIOA.UART1_TXD)
fpioa.set_function(4,FPIOA.UART1_RXD)

uart=UART(UART.UART1,115200) #设置串口号1和波特率

fpioa.set_function(52,FPIOA.GPIO52)
LED=Pin(52,Pin.OUT) #构建led对象，GPIO52,输出

#缓冲区和3.5寸的屏幕图像显示
Display.init(Display.ST7701, to_ide=True)

# 初始化媒体管理器
MediaManager.init()

#启动摄像头传感器模块
sensor.run()

#时钟模块建立
clock = time.clock()

#建立rtc计时器
#rtc = machine.RTC()

#建立触摸屏
tp = TOUCH(0)

#_________________________________________原有功能函数定义_____________________________________________

#找到最大矩形
def find_max_rect(rectangles):
    max_size = 0
    max_rect = None
    for rect in rectangles:
        if rect.w()*rect.h() > max_size :
            max_rect = rect
            max_size = rect.w()*rect.h()
    return max_rect

#拟合矩形 - 简单滤波稳定化
def detect_and_draw_rectangles():
    global rect_corner_points, cx_filter, cy_filter
    img = sensor.snapshot()
    # 转换到灰度图像进行矩形检测
    img_gray = img.to_grayscale(copy=True)
    img.bilateral(1, 30, 30)  # 双边滤波
    # 应用二值化增强边缘
    img_binary = img_gray.binary([binary_threshold])
    rects = img_binary.find_rects(threshold=5000)
    # 清空前一次的角点
    rect_corner_points = []

    if rects:
        max_rect = find_max_rect(rects)
        if max_rect:
            # 原始中心点
            raw_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
            raw_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

            # 滤波后的中心点
            rect_cx = int(cx_filter.update(raw_cx))
            rect_cy = int(cy_filter.update(raw_cy))

            corners = max_rect.corners()
            # 绘制矩形中心点和轮廓
            img.draw_circle(rect_cx, rect_cy, 2, color=(255, 0, 0), thickness=2, fill=True)
            img.draw_rectangle(max_rect.rect(), color=(0, 255, 0), thickness=3)
            img.draw_cross(rect_cx, rect_cy, color=(255, 255, 0), size=10, thickness=2)

            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(255, 0, 0), thickness=2)
    return img

# 添加自适应阈值调整功能
def adaptive_threshold_adjustment():
    """根据环境光线自动调整二值化阈值"""
    global binary_threshold

    # 获取当前图像
    img = sensor.snapshot()
    img_gray = img.to_grayscale(copy=True)

    # 计算图像的平均亮度
    histogram = img_gray.get_histogram()
    total_pixels = img_gray.width() * img_gray.height()

    # 计算加权平均亮度
    weighted_sum = 0
    for i in range(len(histogram.bins())):
        weighted_sum += i * histogram.bins()[i]

    avg_brightness = weighted_sum / total_pixels

    # 根据平均亮度调整阈值
    if avg_brightness < 80:  # 较暗环境
        binary_threshold = (50, 150)
    elif avg_brightness > 150:  # 较亮环境
        binary_threshold = (90, 220)
    else:  # 正常环境
        binary_threshold = (70, 192)

# 添加帧率统计
frame_count = 0
fps_start_time = time.ticks_ms()

def calculate_fps():
    """计算并显示帧率"""
    global frame_count, fps_start_time
    frame_count += 1

    current_time = time.ticks_ms()
    elapsed = time.ticks_diff(current_time, fps_start_time)

    if elapsed >= 1000:  # 每秒更新一次
        fps = frame_count * 1000 / elapsed
        print(f"FPS: {fps:.1f}")
        frame_count = 0
        fps_start_time = current_time
        return fps
    return None

# 初始化变量
last_threshold_adjustment = time.ticks_ms()
threshold_adjustment_interval = 5000  # 每5秒调整一次阈值

# 主循环
while True:
    try:
        clock.tick()
        os.exitpoint()

        # 定期自适应调整阈值
        current_time = time.ticks_ms()
        if time.ticks_diff(current_time, last_threshold_adjustment) > threshold_adjustment_interval:
            adaptive_threshold_adjustment()
            last_threshold_adjustment = current_time

        # 检测并绘制矩形
        img = detect_and_draw_rectangles()

        # 计算并显示FPS
        fps = calculate_fps()
        if fps is not None:
            img.draw_string(img.width() - 100, 10, f"FPS: {fps:.1f}",
                           color=(255, 255, 255), scale=2)

        # 显示当前阈值
        img.draw_string(10, img.height() - 30, f"Threshold: {binary_threshold}",
                       color=(255, 255, 255), scale=2)

        # 显示图像
        Display.show_image(img)

        # 检查触摸屏输入，用于手动调整阈值
        touch_point = tp.read()
        if touch_point:
            x, y = touch_point
            if y < 50:  # 屏幕顶部区域
                if x < img.width() // 2:
                    # 左侧：降低阈值
                    binary_threshold = (max(0, binary_threshold[0] - 5),
                                       max(0, binary_threshold[1] - 5))
                else:
                    # 右侧：提高阈值
                    binary_threshold = (min(255, binary_threshold[0] + 5),
                                       min(255, binary_threshold[1] + 5))
                print(f"手动调整阈值: {binary_threshold}")

    except Exception as e:
        # 错误处理
        print(f"错误: {e}")
        img = sensor.snapshot()
        img.draw_string(10, 10, f"Error: {e}", color=(255, 0, 0), scale=2)
        Display.show_image(img)
        time.sleep(1)
