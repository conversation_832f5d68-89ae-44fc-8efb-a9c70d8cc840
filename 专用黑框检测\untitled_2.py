import time
import os
import sys
import math
import random
import gc

from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import UART
from machine import RTC
from machine import ADC
from machine import TOUCH

from media.sensor import *
from media.display import *
from media.media import *

#______________________________________变量定义并赋初值___________________________________________

#任务标志位，通过串口接受数据改变
Flag = 2


#(0,0,0,0,0,0)
#矩形四点的坐标
rect_corner_points = []

binary_threshold = (70,192)

#_________________________________________模块初始化_____________________________________________

#摄像头传感器初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width = 400, height = 240)
sensor.set_pixformat(Sensor.RGB565)

#串口初始化
fpioa = FPIOA()
# UART1代码
fpioa.set_function(3,FPIOA.UART1_TXD)
fpioa.set_function(4,FPIOA.UART1_RXD)

uart=UART(UART.UART1,115200) #设置串口号1和波特率

fpioa.set_function(52,FPIOA.GPIO52)
LED=Pin(52,Pin.OUT) #构建led对象，GPIO52,输出

#缓冲区和3.5寸的屏幕图像显示
Display.init(Display.ST7701, to_ide=True)

# 初始化媒体管理器
MediaManager.init()

#启动摄像头传感器模块
sensor.run()

#时钟模块建立
clock = time.clock()

#建立rtc计时器
#rtc = machine.RTC()

#建立触摸屏
tp = TOUCH(0)

#_________________________________________原有功能函数定义_____________________________________________

#找到最大矩形
def find_max_rect(rectangles):
    max_size = 0
    max_rect = None
    for rect in rectangles:
        if rect.w()*rect.h() > max_size :
            max_rect = rect
            max_size = rect.w()*rect.h()
    return max_rect

#拟合矩形 - 使用保存的灰度阈值
def detect_and_draw_rectangles():
    global rect_corner_points
    img = sensor.snapshot()
    # 转换到灰度图像进行矩形检测
    img_gray = img.to_grayscale(copy=True)
    img.bilateral(1, 30, 30)  # 双边滤波，空间域、颜色域的标准差分别为 5、75
    # 应用二值化增强边缘
    img_binary = img_gray.binary([binary_threshold])
    rects = img_binary.find_rects(threshold=5000)
    # 清空前一次的角点
    rect_corner_points = []

    if rects:
        max_rect = find_max_rect(rects)
        if max_rect:
            rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
            rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)
            corners = max_rect.corners()
            # 绘制矩形中心点和轮廓
            img.draw_circle(rect_cx, rect_cy, 2, color=(255, 0, 0), thickness=2, fill=True)
            img.draw_rectangle(max_rect.rect(), color=(0, 255, 0), thickness=3)
            img.draw_cross(rect_cx, rect_cy, color=(255, 255, 0), size=10, thickness=2)

            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(255, 0, 0), thickness=2)
    return img

while True:
    clock.tick()
    os.exitpoint()
    img = detect_and_draw_rectangles()
    Display.show_image(img)
