import numpy as np
import cv2
import math

# 全局变量
detected_circles = []  # 存储检测到的所有圆
target_circle = None   # 第三个红色圆
frame_count = 0

def filter_red_regions(frame):
    """筛选红色区域"""
    # 转换到HSV色彩空间，更适合颜色检测
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    
    # 定义红色的HSV范围（红色在HSV中有两个范围）
    # 红色范围1: 0-10
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    
    # 红色范围2: 170-180
    lower_red2 = np.array([170, 50, 50])
    upper_red2 = np.array([180, 255, 255])
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    
    # 合并两个红色范围的掩码
    red_mask = cv2.bitwise_or(mask1, mask2)
    
    # 形态学操作去除噪声
    kernel = np.ones((3, 3), np.uint8)
    red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_OPEN, kernel)
    red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel)
    
    return red_mask

def calculate_circularity(contour):
    """计算轮廓的圆形度"""
    area = cv2.contourArea(contour)
    perimeter = cv2.arcLength(contour, True)
    
    if perimeter == 0:
        return 0
    
    circularity = 4 * math.pi * area / (perimeter * perimeter)
    return circularity

def detect_concentric_circles(frame):
    """检测同心圆并找到第三个红色圆"""
    global detected_circles, target_circle
    
    # 1. 筛选红色区域
    red_mask = filter_red_regions(frame)
    
    # 2. 在红色区域中检测圆形
    gray_red = cv2.bitwise_and(cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY), red_mask)
    gray_red = cv2.medianBlur(gray_red, 5)
    
    # 3. 霍夫圆检测（针对红色区域优化参数）
    circles = cv2.HoughCircles(
        gray_red,
        cv2.HOUGH_GRADIENT,
        dp=1,
        minDist=30,        # 减小最小距离，允许检测同心圆
        param1=50,         # 降低Canny高阈值
        param2=20,         # 降低累加器阈值，更容易检测
        minRadius=15,      # 最小半径
        maxRadius=150      # 最大半径
    )
    
    detected_circles = []
    
    if circles is not None:
        circles = np.uint16(np.around(circles))
        
        # 4. 进一步筛选圆形
        for circle in circles[0, :]:
            x, y, r = circle[0], circle[1], circle[2]
            
            # 提取圆形区域
            mask = np.zeros(gray_red.shape, np.uint8)
            cv2.circle(mask, (x, y), r, 255, -1)
            
            # 检查圆形区域内的红色像素比例
            red_pixels = cv2.countNonZero(cv2.bitwise_and(red_mask, mask))
            total_pixels = cv2.countNonZero(mask)
            red_ratio = red_pixels / total_pixels if total_pixels > 0 else 0
            
            # 只保留红色比例较高的圆
            if red_ratio > 0.3:  # 至少30%的红色像素
                detected_circles.append({
                    'center': (x, y),
                    'radius': r,
                    'red_ratio': red_ratio,
                    'area': math.pi * r * r
                })
    
    # 5. 找到第三个圆（从里往外排序）
    if len(detected_circles) >= 3:
        # 按半径排序（从小到大）
        detected_circles.sort(key=lambda c: c['radius'])
        target_circle = detected_circles[2]  # 第三个圆（索引2）
        
        return True, target_circle
    
    return False, None

def draw_detection_results(frame, show_all=True):
    """绘制检测结果"""
    global detected_circles, target_circle
    
    # 绘制所有检测到的圆
    if show_all:
        for i, circle in enumerate(detected_circles):
            x, y = circle['center']
            r = circle['radius']
            
            # 不同颜色表示不同的圆
            if i == 0:  # 第一个圆（最小）
                color = (255, 0, 0)  # 蓝色
                label = "1st"
            elif i == 1:  # 第二个圆
                color = (0, 255, 0)  # 绿色
                label = "2nd"
            elif i == 2:  # 第三个圆（目标）
                color = (0, 0, 255)  # 红色
                label = "3rd (Target)"
            else:
                color = (255, 255, 0)  # 青色
                label = f"{i+1}th"
            
            # 绘制圆
            cv2.circle(frame, (x, y), r, color, 2)
            cv2.circle(frame, (x, y), 2, color, -1)
            
            # 添加标签
            cv2.putText(frame, f"{label} R:{r}", (x-30, y-r-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    # 特别标记第三个圆
    if target_circle:
        x, y = target_circle['center']
        r = target_circle['radius']
        
        # 绘制粗红色圆圈
        cv2.circle(frame, (x, y), r, (0, 0, 255), 4)
        cv2.circle(frame, (x, y), 5, (0, 0, 255), -1)
        
        # 绘制十字线
        cv2.line(frame, (x-15, y), (x+15, y), (0, 0, 255), 2)
        cv2.line(frame, (x, y-15), (x, y+15), (0, 0, 255), 2)
        
        # 显示详细信息
        info_text = f"Target Circle - Center:({x},{y}) Radius:{r}"
        cv2.putText(frame, info_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        return x, y, r
    
    return None, None, None

# 打开摄像头
cap = cv2.VideoCapture(0)

# 检查摄像头是否成功打开
if not cap.isOpened():
    print("无法打开摄像头")
    exit()

print("红色圆形检测系统启动")
print("按 'q' 退出，按 's' 切换显示模式")

show_all_circles = True

while True:
    # 读取一帧视频
    ret, frame = cap.read()
    
    if not ret:
        print("无法获取帧")
        break
    
    frame_count += 1
    
    # 检测同心圆
    found, target = detect_concentric_circles(frame)
    
    # 绘制检测结果
    target_x, target_y, target_r = draw_detection_results(frame, show_all_circles)
    
    # 显示状态信息
    status_text = f"Circles Found: {len(detected_circles)}"
    cv2.putText(frame, status_text, (10, frame.shape[0] - 60), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    if found and target_circle:
        target_status = f"Target: 3rd Circle Detected!"
        cv2.putText(frame, target_status, (10, frame.shape[0] - 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        # 这里可以添加发送坐标给单片机的代码
        print(f"第三个红色圆 - 中心:({target_x}, {target_y}), 半径:{target_r}")
    else:
        no_target_status = "Searching for 3rd red circle..."
        cv2.putText(frame, no_target_status, (10, frame.shape[0] - 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
    
    # 显示帧数
    cv2.putText(frame, f"Frame: {frame_count}", (frame.shape[1] - 150, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 显示结果
    cv2.imshow('Enhanced Red Circle Detection', frame)
    
    # 键盘控制
    key = cv2.waitKey(1) & 0xFF
    if key == ord('q'):
        break
    elif key == ord('s'):
        show_all_circles = not show_all_circles
        print(f"显示模式切换: {'显示所有圆' if show_all_circles else '仅显示目标圆'}")

# 释放资源
cap.release()
cv2.destroyAllWindows()
print("程序结束")
