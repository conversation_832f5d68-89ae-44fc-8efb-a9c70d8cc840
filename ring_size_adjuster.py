#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
圆环大小调整工具
功能：
1. 手动调整五个圆环的大小
2. 保存调整后的图像
3. 导出调整后的参数
"""

import cv2
import numpy as np
import os
import json
import time

class RingSizeAdjuster:
    def __init__(self):
        # 窗口名称
        self.window_name = "圆环大小调整工具"
        
        # 创建窗口
        cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(self.window_name, 1000, 800)
        
        # 图像尺寸
        self.img_width = 800
        self.img_height = 800
        
        # 圆环参数
        self.center_x = self.img_width // 2
        self.center_y = self.img_height // 2
        self.ring_radii = [40, 80, 120, 160, 200]  # 初始半径值（像素）
        self.ring_colors = [
            (0, 0, 255),    # 红色
            (0, 0, 255),    # 红色
            (0, 0, 255),    # 红色
            (0, 0, 255),    # 红色
            (0, 0, 255)     # 红色
        ]
        self.ring_thickness = [2, 2, 2, 2, 2]  # 线宽
        
        # 高亮第三个圆环
        self.ring_thickness[2] = 3
        
        # 创建滑动条
        self.create_trackbars()
        
        # 创建空白图像
        self.image = np.ones((self.img_height, self.img_width, 3), dtype=np.uint8) * 255
        
        # 背景图像
        self.background_image = None
        
        # 保存参数
        self.params_file = "ring_params.json"
        self.load_params()
        
        # 状态信息
        self.status_message = "按 's' 保存图像，按 'p' 保存参数，按 'r' 重置，按 'q' 退出"
        self.last_action = ""
        
        # 像素/厘米比例（默认值）
        self.pixels_per_cm = 20  # 20像素/厘米
        
    def create_trackbars(self):
        """创建滑动条"""
        # 创建圆心位置滑动条
        cv2.createTrackbar("中心 X", self.window_name, self.center_x, self.img_width, self.on_center_x_change)
        cv2.createTrackbar("中心 Y", self.window_name, self.center_y, self.img_height, self.on_center_y_change)
        
        # 创建像素/厘米比例滑动条
        cv2.createTrackbar("像素/厘米", self.window_name, int(self.pixels_per_cm), 100, self.on_scale_change)
        
        # 创建圆环半径滑动条
        for i in range(5):
            trackbar_name = f"环 {i+1} 半径 ({(i+1)*2}cm)"
            cv2.createTrackbar(trackbar_name, self.window_name, self.ring_radii[i], 400, lambda v, idx=i: self.on_radius_change(v, idx))
            
        # 创建线宽滑动条
        for i in range(5):
            trackbar_name = f"环 {i+1} 线宽"
            cv2.createTrackbar(trackbar_name, self.window_name, self.ring_thickness[i], 10, lambda v, idx=i: self.on_thickness_change(v, idx))
    
    def on_center_x_change(self, value):
        """圆心X坐标变化回调"""
        self.center_x = value
        self.update_image()
    
    def on_center_y_change(self, value):
        """圆心Y坐标变化回调"""
        self.center_y = value
        self.update_image()
    
    def on_scale_change(self, value):
        """像素/厘米比例变化回调"""
        if value < 1:
            value = 1
        self.pixels_per_cm = value
        
        # 根据新比例更新圆环半径
        for i in range(5):
            cm_radius = (i + 1) * 2  # 2, 4, 6, 8, 10 cm
            new_radius = int(cm_radius * self.pixels_per_cm)
            self.ring_radii[i] = new_radius
            cv2.setTrackbarPos(f"环 {i+1} 半径 ({(i+1)*2}cm)", self.window_name, new_radius)
        
        self.update_image()
    
    def on_radius_change(self, value, ring_index):
        """圆环半径变化回调"""
        self.ring_radii[ring_index] = value
        self.update_image()
    
    def on_thickness_change(self, value, ring_index):
        """线宽变化回调"""
        if value < 1:
            value = 1
        self.ring_thickness[ring_index] = value
        self.update_image()
    
    def load_background(self, image_path):
        """加载背景图像"""
        if os.path.exists(image_path):
            try:
                img = cv2.imread(image_path)
                if img is not None:
                    # 调整大小以适应窗口
                    self.background_image = cv2.resize(img, (self.img_width, self.img_height))
                    print(f"已加载背景图像: {image_path}")
                    self.update_image()
                    return True
                else:
                    print(f"无法读取图像: {image_path}")
            except Exception as e:
                print(f"加载图像时出错: {e}")
        else:
            print(f"图像文件不存在: {image_path}")
        return False
    
    def update_image(self):
        """更新图像显示"""
        # 创建空白图像或使用背景图像
        if self.background_image is not None:
            self.image = self.background_image.copy()
        else:
            self.image = np.ones((self.img_height, self.img_width, 3), dtype=np.uint8) * 255
            
            # 绘制黑色边框（模拟A4纸的黑色胶带边框）
            border_width = int(1.8 * self.pixels_per_cm)  # 1.8cm宽的黑色胶带
            cv2.rectangle(self.image, 
                         (border_width, border_width), 
                         (self.img_width - border_width, self.img_height - border_width), 
                         (0, 0, 0), border_width)
        
        # 绘制圆环
        for i in range(5):
            cv2.circle(self.image, (self.center_x, self.center_y), 
                      self.ring_radii[i], self.ring_colors[i], self.ring_thickness[i])
        
        # 绘制圆心
        cv2.circle(self.image, (self.center_x, self.center_y), 3, (0, 0, 255), -1)
        
        # 显示当前参数
        y_offset = 30
        cv2.putText(self.image, f"中心: ({self.center_x}, {self.center_y})", 
                   (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        y_offset += 25
        
        cv2.putText(self.image, f"像素/厘米: {self.pixels_per_cm}", 
                   (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        y_offset += 25
        
        for i in range(5):
            cv2.putText(self.image, f"环 {i+1}: {self.ring_radii[i]}px ({self.ring_radii[i]/self.pixels_per_cm:.1f}cm)", 
                       (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
            y_offset += 25
        
        # 显示状态信息
        cv2.putText(self.image, self.status_message, 
                   (10, self.img_height - 40), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        if self.last_action:
            cv2.putText(self.image, self.last_action, 
                       (10, self.img_height - 15), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        
        # 显示图像
        cv2.imshow(self.window_name, self.image)
    
    def save_image(self):
        """保存当前图像"""
        filename = f"adjusted_rings_{int(time.time())}.jpg"
        cv2.imwrite(filename, self.image)
        self.last_action = f"图像已保存: {filename}"
        print(f"图像已保存: {filename}")
        self.update_image()
    
    def save_params(self):
        """保存当前参数"""
        params = {
            "center_x": self.center_x,
            "center_y": self.center_y,
            "pixels_per_cm": self.pixels_per_cm,
            "ring_radii": self.ring_radii,
            "ring_thickness": self.ring_thickness,
            "timestamp": time.time()
        }
        
        with open(self.params_file, "w") as f:
            json.dump(params, f, indent=4)
        
        self.last_action = f"参数已保存: {self.params_file}"
        print(f"参数已保存: {self.params_file}")
        self.update_image()
    
    def load_params(self):
        """加载参数"""
        if os.path.exists(self.params_file):
            try:
                with open(self.params_file, "r") as f:
                    params = json.load(f)
                
                self.center_x = params.get("center_x", self.center_x)
                self.center_y = params.get("center_y", self.center_y)
                self.pixels_per_cm = params.get("pixels_per_cm", self.pixels_per_cm)
                self.ring_radii = params.get("ring_radii", self.ring_radii)
                self.ring_thickness = params.get("ring_thickness", self.ring_thickness)
                
                print(f"已加载参数: {self.params_file}")
                return True
            except Exception as e:
                print(f"加载参数时出错: {e}")
        return False
    
    def reset_params(self):
        """重置参数"""
        self.center_x = self.img_width // 2
        self.center_y = self.img_height // 2
        self.pixels_per_cm = 20
        
        # 更新滑动条
        cv2.setTrackbarPos("中心 X", self.window_name, self.center_x)
        cv2.setTrackbarPos("中心 Y", self.window_name, self.center_y)
        cv2.setTrackbarPos("像素/厘米", self.window_name, int(self.pixels_per_cm))
        
        # 更新圆环半径
        for i in range(5):
            cm_radius = (i + 1) * 2  # 2, 4, 6, 8, 10 cm
            self.ring_radii[i] = int(cm_radius * self.pixels_per_cm)
            cv2.setTrackbarPos(f"环 {i+1} 半径 ({(i+1)*2}cm)", self.window_name, self.ring_radii[i])
        
        # 更新线宽
        for i in range(5):
            thickness = 2
            if i == 2:  # 第三环加粗
                thickness = 3
            self.ring_thickness[i] = thickness
            cv2.setTrackbarPos(f"环 {i+1} 线宽", self.window_name, self.ring_thickness[i])
        
        self.last_action = "参数已重置"
        print("参数已重置")
        self.update_image()
    
    def run(self):
        """运行主循环"""
        print("=" * 50)
        print("圆环大小调整工具")
        print("=" * 50)
        print("控制说明:")
        print("- 使用滑动条调整圆环参数")
        print("- 's': 保存当前图像")
        print("- 'p': 保存当前参数")
        print("- 'r': 重置参数")
        print("- 'b': 加载背景图像")
        print("- 'q': 退出程序")
        print("=" * 50)
        
        self.update_image()
        
        while True:
            key = cv2.waitKey(100) & 0xFF
            
            if key == ord('q'):
                break
            elif key == ord('s'):
                self.save_image()
            elif key == ord('p'):
                self.save_params()
            elif key == ord('r'):
                self.reset_params()
            elif key == ord('b'):
                image_path = input("请输入背景图像路径: ")
                self.load_background(image_path)
        
        cv2.destroyAllWindows()

def main():
    adjuster = RingSizeAdjuster()
    adjuster.run()

if __name__ == "__main__":
    main()
