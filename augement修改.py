import time
import os
import sys
import math
import random
import gc

from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import UART
from machine import RTC
from machine import ADC
from machine import TOUCH

from media.sensor import *
from media.display import *
from media.media import *

#______________________________________变量定义并赋初值___________________________________________

#任务标志位，通过串口接受数据改变
Flag = 3

# Flag = 0   正常拍照
# Flag = 1   脱机调整阈值并且存储
# Flag = 2   基础部分第二题
# Flag = 3   基础部分第三题
# Flag = 4   发挥部分第一题
# Flag = 5   发挥部分第二题
# Flag = 6   发挥部分第三题
# Flag = 7   其他部分

# 新增：性能优化变量
max_frame_skip = 0  # 不跳帧，提高帧率
frame_skip_count = 0  # 跳帧计数

# 性能优化：减少不必要的计算
last_detection_time = 0
detection_interval = 50  # 检测间隔(ms)

# 动态分辨率切换系统
current_resolution = "HD"  # 当前分辨率状态："HD"(800x480) 或 "QVGA"(320x240)
resolution_switch_delay = 100  # 分辨率切换延迟(ms)
last_resolution_switch = 0
resolution_switching = False  # 分辨率切换中标志，提高帧率

# 脱机调阈值系统的全局变量
threshold_adjustment_mode = False  # 是否进入阈值调节模式
current_mode = 'lab'  # 当前工作模式：'lab' 或 'gray'
lab_threshold = [0, 100, -128, 127, -128, 127]  # LAB阈值
gray_threshold = [0, 255]  # 灰度阈值
saved_lab_thresholds = []  # 保存的LAB阈值列表
saved_gray_thresholds = []  # 保存的灰度阈值列表

#颜色阈值
blue_threshold = (30, 50, -50, -15, -10, 30)
red_threshold = (30, 100, 15, 127, 15, 127)  # 红色圆检测阈值
binary_threshold = (60, 255)

#矩形中心的坐标
rect_cx = None
rect_cy = None

#屏幕中心
target_2_cx = 400
target_2_cy = 240

#触摸次数
touch_counter = 0

# 图像裁剪ROI（用于阈值调节）
cut_roi = (160, 120, 480, 240)



# 黑框白底检测相关变量 (Flag==8)
thr_white = (60, 100, -20, 20, -20, 20)  # 白色阈值
thr_black = (0, 35, -20, 20, -20, 20)    # 黑色阈值
target_offset_bw = [-1, -1]  # 目标补偿量
target_bw = [0, 0]           # 目标坐标
view_offset_bw = [0, 0]      # 视角偏移补偿量
roi_size_bw = 240            # ROI区域大小

#_________________________________________模块初始化_____________________________________________

#摄像头传感器初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width = 800, height = 480)
sensor.set_pixformat(Sensor.RGB565)

#串口初始化
fpioa = FPIOA()
# UART1代码
fpioa.set_function(3,FPIOA.UART1_TXD)
fpioa.set_function(4,FPIOA.UART1_RXD)

uart=UART(UART.UART1,115200) #设置串口号1和波特率

#fpioa.set_function(52,FPIOA.GPIO52)
#LED=Pin(52,Pin.OUT) #构建led对象，GPIO52,输出

#缓冲区和3.5寸的屏幕图像显示
Display.init(Display.ST7701, to_ide=True)

# 初始化媒体管理器
MediaManager.init()

#启动摄像头传感器模块
sensor.run()

#时钟模块建立
clock = time.clock()

#建立触摸屏
tp = TOUCH(0)

print("矩形检测瞄准系统初始化完成")

#__________________________________________动态分辨率切换系统_____________________________________________

def sensor_QVGA():
    """切换到QVGA分辨率用于黑框识别（高帧率）"""
    global current_resolution

    if current_resolution == "QVGA":
        return True  # 已经是QVGA分辨率

    try:
        # 停止传感器
        sensor.stop()
        time.sleep_ms(30)

        # 重新配置为QVGA分辨率
        sensor.reset()
        sensor.set_framesize(width=320, height=240)
        sensor.set_pixformat(Sensor.RGB565)

        # 重新启动
        sensor.run()
        time.sleep_ms(50)  # 等待稳定

        current_resolution = "QVGA"
        return True

    except Exception as e:
        print(f"QVGA切换失败: {e}")
        return False

def sensor_HD():
    """切换到HD分辨率用于色块识别和阈值调节（高精度）"""
    global current_resolution

    if current_resolution == "HD":
        return True  # 已经是HD分辨率

    try:
        # 停止传感器
        sensor.stop()
        time.sleep_ms(30)

        # 重新配置为HD分辨率
        sensor.reset()
        sensor.set_framesize(width=800, height=480)
        sensor.set_pixformat(Sensor.RGB565)

        # 重新启动
        sensor.run()
        time.sleep_ms(50)  # 等待稳定

        current_resolution = "HD"
        return True

    except Exception as e:
        print(f"HD切换失败: {e}")
        return False

def get_current_resolution():
    """获取当前分辨率信息"""
    if current_resolution == "HD":
        return 800, 480
    else:
        return 320, 240

# QVGA黑框识别包装函数
def detect_black_frame_QVGA():
    """
    QVGA高帧率黑框识别包装函数
    返回: (是否找到目标, 目标中心X, 目标中心Y, 误差X, 误差Y)
    """
    global rect_cx, rect_cy

    # 自动切换到QVGA分辨率
    sensor_QVGA()

    img = sensor.snapshot()
    width, height = get_current_resolution()
    center_x = width // 2
    center_y = height // 2

    # 使用二值化方法检测黑框
    rects = img_bin_rects(img)

    target_found = False
    e_x_hd = 0
    e_y_hd = 0

    if rects:
        max_rect = find_max_rect(rects)
        if max_rect:
            # 计算矩形中心点
            rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
            rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

            # 绘制矩形中心点和轮廓
            img.draw_circle(rect_cx, rect_cy, 3, color=(255, 0, 0), thickness=2, fill=True)

            corners = max_rect.corners()
            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(0, 255, 0), thickness=2)

            # 计算误差（相对于QVGA中心点）
            e_x = center_x - rect_cx
            e_y = center_y - rect_cy

            # 将QVGA坐标转换为HD坐标系用于控制
            e_x_hd = int(e_x * 2.5)  # QVGA->HD X轴缩放
            e_y_hd = int(e_y * 2.0)  # QVGA->HD Y轴缩放

            distance = calculate_distance(rect_cx, rect_cy, center_x, center_y)
            target_found = True

            # 显示信息
            img.draw_string_advanced(5, 5, 10, f"黑框跟踪 QVGA", color=(0, 255, 0))
            img.draw_string_advanced(5, 16, 8, f"中心: ({rect_cx}, {rect_cy})", color=(255, 255, 255))
            img.draw_string_advanced(5, 27, 8, f"误差: ({e_x}, {e_y})", color=(255, 255, 255))
            img.draw_string_advanced(5, 38, 8, f"距离: {distance:.1f}px", color=(255, 255, 255))
    else:
        img.draw_string_advanced(5, 5, 12, "搜索黑框中... QVGA", color=(255, 255, 0))

    # 显示帧率信息
    fps = clock.fps()
    img.draw_string_advanced(5, height-15, 8, f"FPS: {fps:.1f} {width}x{height}", color=(255, 255, 255))

    # 绘制中心十字线
    img.draw_cross(center_x, center_y, color=(0, 0, 255), size=6, thickness=1)

    Display.show_image(img)

    return target_found, rect_cx if target_found else 0, rect_cy if target_found else 0, e_x_hd, e_y_hd

# 简单的激光发射函数（保持兼容性）
def fire_laser():
    """简单的激光发射函数"""
    try:
        # 发送激光发射指令
        laser_command = [0x2c, 0x13, 0x01, 0x5B]
        safe_uart_send(laser_command)
        return True
    except Exception as e:
        print(f"激光发射错误: {e}")
        return False

#__________________________________________功能函数定义_____________________________________________

#任务标志位改变函数
def Flag_transform():
    global Flag
    try:
        # 读取3个字节
        data = uart.read()
        # 如果读取到数据且长度为3
        if data is not None and len(data) == 3:
            # 检查包头和包尾
            if data[0] == 0xAA and data[2] == 0xAB:
                Flag = data[1]
                print(f"Flag更新为: {Flag}")
    except Exception as e:
        print(f"串口数据处理错误: {e}")

#存储脱机调整的阈值
def storage():
    global blue_threshold, binary_threshold
    try:
        # 使用保存的LAB阈值，如果没有则使用默认值
        if saved_lab_thresholds:
            blue_threshold = tuple(saved_lab_thresholds[-1])   # 使用最后一个保存的LAB阈值作为蓝色
        else:
            blue_threshold = blue_threshold  # 使用默认蓝色阈值

        # 使用保存的灰度阈值，如果没有则使用默认值
        if saved_gray_thresholds:
            binary_threshold = saved_gray_thresholds[-1]  # 使用最后保存的阈值
        else:
            binary_threshold = binary_threshold  # 默认阈值

        print(f"阈值已更新 - 蓝色: {blue_threshold}, 二值化: {binary_threshold}")
    except Exception as e:
        print(f"阈值存储错误: {e}")

#找到对应最大色块
def find_max_blob(blobs):
    if not blobs:
        return None
    max_size = 0
    max_blob = None
    for b in blobs:
        if b[2]*b[3] > max_size:
            max_blob = b
            max_size = b[2]*b[3]
    return max_blob

#找到最大矩形
def find_max_rect(rects):
    if not rects:
        return None
    max_size = 0
    max_rect = None
    for rect in rects:
        if rect.w()*rect.h() > max_size:
            max_rect = rect
            max_size = rect.w()*rect.h()
    return max_rect

#使用灰度阈值寻找黑色边框 - 修复版本
def img_bin_rects(img):
    global binary_threshold
    try:
        # 转换到灰度图像进行矩形检测
        img_gray = img.to_grayscale(copy=True)

        # 应用二值化增强边缘
        img_binary = img_gray.binary([binary_threshold])
        rects = img_binary.find_rects(threshold=10000)

        if rects:
            return rects
        else:
            return None
    except Exception as e:
        print(f"矩形检测错误: {e}")
        return None

# 安全的串口发送函数
def safe_uart_send(data_list):
    """安全的串口发送函数，包含错误处理"""
    try:
        uart.write(bytes(data_list))
        return True
    except Exception as e:
        print(f"串口发送错误: {e}")
        return False

# 计算两点距离
def calculate_distance(x1, y1, x2, y2):
    """计算两点之间的距离"""
    return math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2)

# 激光笔控制函数
def fire_laser():
    """发射激光笔"""
    try:
        # 发送激光发射信号给单片机
        n = 9 & 0xFF
        send_lst = [0x2c, 0x12, n, 0x5B]
        safe_uart_send(send_lst)

        print("激光笔发射!")
        return True
    except Exception as e:
        print(f"激光发射错误: {e}")
        return False

#Flag==1 - 黑框识别（可选QVGA高帧率）
def goto_target_point():
    """黑框识别 - 可选择使用QVGA高帧率模式"""
    global rect_cx, rect_cy

    # 使用QVGA黑框识别包装函数
    target_found, target_x, target_y, e_x_hd, e_y_hd = detect_black_frame_QVGA()

    if target_found:
        # 发送控制信号（已经转换为HD坐标系）
        e_x_high = (e_x_hd >> 8) & 0xFF
        e_x_low = e_x_hd & 0xFF
        e_y_high = (e_y_hd >> 8) & 0xFF
        e_y_low = e_y_hd & 0xFF

        send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
        safe_uart_send(send_lst)

        # 检查激光发射条件
        distance = calculate_distance(target_x, target_y, 160, 120)  # QVGA中心点
        if distance < 10:
            fire_laser()

#Flag==2 - 基础部分第二题（可选QVGA黑框识别）
def goto_target_point_1():
    """基础部分第二题 - 可选择使用QVGA黑框识别提高帧率"""
    global rect_cx, rect_cy

    # 选择1：使用QVGA高帧率黑框识别
    use_qvga_detection = True  # 设置为True使用QVGA，False使用HD

    if use_qvga_detection:
        # 使用QVGA黑框识别
        target_found, target_x, target_y, e_x_hd, e_y_hd = detect_black_frame_QVGA()

        if target_found:
            # 发送控制信号（已经转换为HD坐标系）
            e_x_high = (e_x_hd >> 8) & 0xFF
            e_x_low = e_x_hd & 0xFF
            e_y_high = (e_y_hd >> 8) & 0xFF
            e_y_low = e_y_hd & 0xFF

            send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
            safe_uart_send(send_lst)

            # 检查激光发射条件
            distance = calculate_distance(target_x, target_y, 160, 120)
            if distance < 10:
                fire_laser()
        return

    # 选择2：使用HD分辨率（原有方法）
    sensor_HD()

    img = sensor.snapshot()
    rects = img_bin_rects(img)
    blue_blobs = img.find_blobs([blue_threshold], pixels_threshold=10, area_threshold=10)

    if not blue_blobs:
        if rects:
            max_rect = find_max_rect(rects)
            if max_rect:
                rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

                # 绘制矩形中心点和轮廓
                img.draw_circle(rect_cx, rect_cy, 5, color=(255, 0, 0), thickness=2, fill=True)

                corners = max_rect.corners()
                for i in range(4):
                    start_idx = i
                    end_idx = (i + 1) % 4
                    img.draw_line(corners[start_idx][0], corners[start_idx][1],
                                 corners[end_idx][0], corners[end_idx][1],
                                 color=(0, 255, 0), thickness=2)

                # 计算误差
                e_x_5 = 400 - rect_cx
                e_y_5 = 240 - rect_cy
                distance = calculate_distance(rect_cx, rect_cy, 400, 240)

                # 发送控制信号
                e_x_5_high = (e_x_5 >> 8) & 0xFF
                e_x_5_low = e_x_5 & 0xFF
                e_y_5_high = (e_y_5 >> 8) & 0xFF
                e_y_5_low = e_y_5 & 0xFF

                send_lst = [0x2c, 0x12, e_x_5_high, e_x_5_low, e_y_5_high, e_y_5_low, 0x5B]
                safe_uart_send(send_lst)

                # 检查激光发射条件
                if distance < 20:
                    fire_laser()
#                    img.draw_string_advanced(10, 40, 20, "激光发射!", color=(255, 0, 0))

                # 显示信息
#                img.draw_string_advanced(10, 10, 16, f"矩形跟踪: ({rect_cx}, {rect_cy})", color=(0, 255, 0))
#                img.draw_string_advanced(10, 60, 16, f"距离: {distance:.1f}px", color=(255, 255, 255))
    else:
        max_blue_blob = find_max_blob(blue_blobs)
        if max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            if rects:
                max_rect = find_max_rect(rects)
                if max_rect:
                    rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                    rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

                    e_x_6 = rect_cx - max_blue_blob.cx()
                    e_y_6 = rect_cy - max_blue_blob.cy()

                    e_x_6_high = (e_x_6 >> 8) & 0xFF
                    e_x_6_low = e_x_6 & 0xFF
                    e_y_6_high = (e_y_6 >> 8) & 0xFF
                    e_y_6_low = e_y_6 & 0xFF

                    send_lst = [0x2c, 0x12, e_x_6_high, e_x_6_low, e_y_6_high, e_y_6_low, 0x5B]
                    safe_uart_send(send_lst)

#                    img.draw_string_advanced(10, 10, 16, "双目标相对跟踪", color=(255, 255, 0))
    Display.show_image(img)

#Flag==3 - 基础部分第三题（可选QVGA黑框识别）
def goto_target_point_2():
    """基础部分第三题 - 可选择使用QVGA黑框识别提高帧率"""
    global rect_cx, rect_cy

    # 选择1：使用QVGA高帧率黑框识别
    use_qvga_detection = True  # 设置为True使用QVGA，False使用HD

    if use_qvga_detection:
        # 使用QVGA黑框识别
        target_found, target_x, target_y, e_x_hd, e_y_hd = detect_black_frame_QVGA()

        if target_found:
            # 发送控制信号（已经转换为HD坐标系）
            e_x_high = (e_x_hd >> 8) & 0xFF
            e_x_low = e_x_hd & 0xFF
            e_y_high = (e_y_hd >> 8) & 0xFF
            e_y_low = e_y_hd & 0xFF

            send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
            safe_uart_send(send_lst)

            # 检查激光发射条件
            distance = calculate_distance(target_x, target_y, 160, 120)
            if distance < 10:
                fire_laser()
        return

    # 选择2：使用HD分辨率（原有方法）
    sensor_HD()

    img = sensor.snapshot()
    rects = img_bin_rects(img)
    blue_blobs = img.find_blobs([blue_threshold], pixels_threshold=10, area_threshold=10)

    if not blue_blobs:
        if not rects:
            # 旋转找黑色矩形框
            v = 8
            V = v & 0xFF
            send_lst = [0x2c, 0x12, V, 0x5B]
            safe_uart_send(send_lst)
            # 减少字符串绘制以提高帧率
            # img.draw_string_advanced(10, 10, 20, "搜索矩形中...", color=(255, 255, 0))
        else:
            max_rect = find_max_rect(rects)
            if max_rect:
                rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

                # 绘制矩形中心点和轮廓
                img.draw_circle(rect_cx, rect_cy, 5, color=(255, 0, 0), thickness=2, fill=True)

                corners = max_rect.corners()
                for i in range(4):
                    start_idx = i
                    end_idx = (i + 1) % 4
                    img.draw_line(corners[start_idx][0], corners[start_idx][1],
                                 corners[end_idx][0], corners[end_idx][1],
                                 color=(0, 255, 0), thickness=2)

                # 计算误差
                e_x_5 = 400 - rect_cx
                e_y_5 = 240 - rect_cy
                distance = calculate_distance(rect_cx, rect_cy, 400, 240)

                # 发送控制信号
                e_x_5_high = (e_x_5 >> 8) & 0xFF
                e_x_5_low = e_x_5 & 0xFF
                e_y_5_high = (e_y_5 >> 8) & 0xFF
                e_y_5_low = e_y_5 & 0xFF

                send_lst = [0x2c, 0x12, e_x_5_high, e_x_5_low, e_y_5_high, e_y_5_low, 0x5B]
                safe_uart_send(send_lst)

                # 检查激光发射条件
                if distance < 20:
                    fire_laser()
#                    img.draw_string_advanced(10, 40, 20, "激光发射!", color=(255, 0, 0))

                # 显示信息
#                img.draw_string_advanced(10, 10, 16, f"矩形跟踪: ({rect_cx}, {rect_cy})", color=(0, 255, 0))
#                img.draw_string_advanced(10, 60, 16, f"距离: {distance:.1f}px", color=(255, 255, 255))
    else:
        max_blue_blob = find_max_blob(blue_blobs)
        if max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            if rects:
                max_rect = find_max_rect(rects)
                if max_rect:
                    rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                    rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

                    e_x_6 = rect_cx - max_blue_blob.cx()
                    e_y_6 = rect_cy - max_blue_blob.cy()

                    e_x_6_high = (e_x_6 >> 8) & 0xFF
                    e_x_6_low = e_x_6 & 0xFF
                    e_y_6_high = (e_y_6 >> 8) & 0xFF
                    e_y_6_low = e_y_6 & 0xFF

                    send_lst = [0x2c, 0x12, e_x_6_high, e_x_6_low, e_y_6_high, e_y_6_low, 0x5B]
                    safe_uart_send(send_lst)

#                    img.draw_string_advanced(10, 10, 16, "双目标相对跟踪", color=(255, 255, 0))

    # 绘制屏幕中心
    img.draw_cross(400, 240, color=(0, 0, 255), size=15, thickness=2)
    img.draw_circle(400, 240, 20, color=(0, 0, 255), thickness=1)

    Display.show_image(img)

#Flag==4,5 - 发挥部分第一、二题（可选QVGA黑框识别）
def goto_target_point_3():
    """发挥部分第一、二题 - 可选择使用QVGA黑框识别提高帧率"""
    global rect_cx, rect_cy

    # 选择1：使用QVGA高帧率黑框识别
    use_qvga_detection = True  # 设置为True使用QVGA，False使用HD

    if use_qvga_detection:
        # 使用QVGA黑框识别
        target_found, target_x, target_y, e_x_hd, e_y_hd = detect_black_frame_QVGA()

        if target_found:
            # 发送控制信号（已经转换为HD坐标系）
            e_x_high = (e_x_hd >> 8) & 0xFF
            e_x_low = e_x_hd & 0xFF
            e_y_high = (e_y_hd >> 8) & 0xFF
            e_y_low = e_y_hd & 0xFF

            send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
            safe_uart_send(send_lst)

            # 检查激光发射条件
            distance = calculate_distance(target_x, target_y, 160, 120)
            if distance < 10:
                fire_laser()
        return

    # 选择2：使用HD分辨率（原有方法）
    sensor_HD()

    img = sensor.snapshot()
    rects = img_bin_rects(img)
    blue_blobs = img.find_blobs([blue_threshold], pixels_threshold=10, area_threshold=10)

    if rects and blue_blobs:
        max_rect = find_max_rect(rects)
        max_blue_blob = find_max_blob(blue_blobs)

        if max_rect and max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
            rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

            # 绘制矩形轮廓
            corners = max_rect.corners()
            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(0, 255, 0), thickness=2)

            img.draw_circle(rect_cx, rect_cy, 5, color=(255, 0, 0), thickness=2, fill=True)

            e_x_7 = rect_cx - max_blue_blob.cx()
            e_y_7 = rect_cy - max_blue_blob.cy()

            e_x_7_high = (e_x_7 >> 8) & 0xFF
            e_x_7_low = e_x_7 & 0xFF
            e_y_7_high = (e_y_7 >> 8) & 0xFF
            e_y_7_low = e_y_7 & 0xFF

            send_lst = [0x2c, 0x12, e_x_7_high, e_x_7_low, e_y_7_high, e_y_7_low, 0x5B]
            safe_uart_send(send_lst)

            # 显示详细信息
            distance = calculate_distance(rect_cx, rect_cy, max_blue_blob.cx(), max_blue_blob.cy())
#            img.draw_string_advanced(10, 10, 16, f"高精度跟踪", color=(255, 255, 255))
#            img.draw_string_advanced(10, 30, 16, f"矩形: ({rect_cx}, {rect_cy})", color=(0, 255, 0))
#            img.draw_string_advanced(10, 50, 16, f"蓝色: ({max_blue_blob.cx()}, {max_blue_blob.cy()})", color=(255, 255, 0))
#            img.draw_string_advanced(10, 70, 16, f"距离: {distance:.1f}px", color=(255, 255, 255))
#    else:
        # 减少字符串绘制以提高帧率
        # img.draw_string_advanced(10, 10, 20, "等待双目标出现...", color=(255, 0, 0))

    Display.show_image(img)



# 生成圆形绘画路径点
def generate_circle_drawing_points(cx, cy, radius, num_points=24):
    """生成圆形绘画的路径点"""
    points = []
    angle_step = 2 * math.pi / num_points

    for i in range(num_points + 1):  # +1确保闭合圆形
        angle = i * angle_step
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        points.append((x, y))

    return points

# 发送绘画控制指令
def send_drawing_command(command_type, x=0, y=0):
    """
    发送绘画控制指令给单片机
    command_type: 1=移动到位置, 2=开始绘画, 3=停止绘画, 4=绘画完成
    """
    try:
        # 数据包格式: [包头, 命令类型, X坐标高位, X坐标低位, Y坐标高位, Y坐标低位, 包尾]
        x_high = (x >> 8) & 0xFF
        x_low = x & 0xFF
        y_high = (y >> 8) & 0xFF
        y_low = y & 0xFF

        send_lst = [0x2C, 0x12, command_type, x_high, x_low, y_high, y_low, 0x5B]
        safe_uart_send(send_lst)

        print(f"发送绘画指令: 类型={command_type}, 坐标=({x}, {y})")
        return True
    except Exception as e:
        print(f"绘画指令发送错误: {e}")
        return False

#Flag==2 - 基础部分第二题（可选QVGA黑框识别）
def goto_target_point_1():
    """基础部分第二题 - 可选择使用QVGA黑框识别提高帧率"""
    global rect_cx, rect_cy

    # 选择使用QVGA高帧率黑框识别
    use_qvga_detection = True  # 设置为True使用QVGA，False使用HD

    if use_qvga_detection:
        # 使用QVGA黑框识别
        target_found, target_x, target_y, e_x_hd, e_y_hd = detect_black_frame_QVGA()

        if target_found:
            # 发送控制信号（已经转换为HD坐标系）
            e_x_high = (e_x_hd >> 8) & 0xFF
            e_x_low = e_x_hd & 0xFF
            e_y_high = (e_y_hd >> 8) & 0xFF
            e_y_low = e_y_hd & 0xFF

            send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
            safe_uart_send(send_lst)

            # 检查激光发射条件
            distance = calculate_distance(target_x, target_y, 160, 120)
            if distance < 10:
                fire_laser()
        return

    # 选择2：使用HD分辨率（原有方法）
    sensor_HD()

    img = sensor.snapshot()
    rects = img_bin_rects(img)
    blue_blobs = img.find_blobs([blue_threshold], pixels_threshold=10, area_threshold=10)

    if rects:
        max_rect = find_max_rect(rects)
        if max_rect:
            rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
            rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

            # 绘制矩形中心点和轮廓
            img.draw_circle(rect_cx, rect_cy, 5, color=(255, 0, 0), thickness=2, fill=True)

            corners = max_rect.corners()
            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(0, 255, 0), thickness=2)

            # 计算误差
            e_x_5 = 400 - rect_cx
            e_y_5 = 240 - rect_cy
            distance = calculate_distance(rect_cx, rect_cy, 400, 240)

            # 发送控制信号
            e_x_5_high = (e_x_5 >> 8) & 0xFF
            e_x_5_low = e_x_5 & 0xFF
            e_y_5_high = (e_y_5 >> 8) & 0xFF
            e_y_5_low = e_y_5 & 0xFF

            send_lst = [0x2c, 0x12, e_x_5_high, e_x_5_low, e_y_5_high, e_y_5_low, 0x5B]
            safe_uart_send(send_lst)

            # 检查激光发射条件
            if distance < 20:
                fire_laser()

            # 显示信息
            img.draw_string_advanced(10, 10, 16, f"HD矩形跟踪: ({rect_cx}, {rect_cy})", color=(0, 255, 0))
            img.draw_string_advanced(10, 30, 16, f"距离: {distance:.1f}px", color=(255, 255, 255))
    else:
        max_blue_blob = find_max_blob(blue_blobs)
        if max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            if rects:
                max_rect = find_max_rect(rects)
                if max_rect:
                    rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                    rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

                    e_x_6 = rect_cx - max_blue_blob.cx()
                    e_y_6 = rect_cy - max_blue_blob.cy()

                    e_x_6_high = (e_x_6 >> 8) & 0xFF
                    e_x_6_low = e_x_6 & 0xFF
                    e_y_6_high = (e_y_6 >> 8) & 0xFF
                    e_y_6_low = e_y_6 & 0xFF

                    send_lst = [0x2c, 0x12, e_x_6_high, e_x_6_low, e_y_6_high, e_y_6_low, 0x5B]
                    safe_uart_send(send_lst)

                    img.draw_string_advanced(10, 10, 16, "双目标相对跟踪 HD", color=(255, 255, 0))

    # 显示帧率
    fps = clock.fps()
    img.draw_string_advanced(10, 50, 16, f"FPS: {fps:.1f} HD", color=(255, 255, 255))

    Display.show_image(img)

#Flag==3 - 基础部分第三题（可选QVGA黑框识别）
def goto_target_point_2():
    """基础部分第三题 - 可选择使用QVGA黑框识别提高帧率"""
    global rect_cx, rect_cy

    # 选择1：使用QVGA高帧率黑框识别
    use_qvga_detection = True  # 设置为True使用QVGA，False使用HD

    if use_qvga_detection:
        # 使用QVGA黑框识别
        target_found, target_x, target_y, e_x_hd, e_y_hd = detect_black_frame_QVGA()

        if target_found:
            # 发送控制信号（已经转换为HD坐标系）
            e_x_high = (e_x_hd >> 8) & 0xFF
            e_x_low = e_x_hd & 0xFF
            e_y_high = (e_y_hd >> 8) & 0xFF
            e_y_low = e_y_hd & 0xFF

            send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
            safe_uart_send(send_lst)

            # 检查激光发射条件
            distance = calculate_distance(target_x, target_y, 160, 120)
            if distance < 10:
                fire_laser()
        return

    # 选择2：使用HD分辨率（原有方法）
    sensor_HD()

    img = sensor.snapshot()
    rects = img_bin_rects(img)
    blue_blobs = img.find_blobs([blue_threshold], pixels_threshold=10, area_threshold=10)

    if rects:
        max_rect = find_max_rect(rects)
        if max_rect:
            rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
            rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

            # 绘制矩形中心点和轮廓
            img.draw_circle(rect_cx, rect_cy, 5, color=(255, 0, 0), thickness=2, fill=True)

            corners = max_rect.corners()
            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(0, 255, 0), thickness=2)

            # 计算误差
            e_x_5 = 400 - rect_cx
            e_y_5 = 240 - rect_cy
            distance = calculate_distance(rect_cx, rect_cy, 400, 240)

            # 发送控制信号
            e_x_5_high = (e_x_5 >> 8) & 0xFF
            e_x_5_low = e_x_5 & 0xFF
            e_y_5_high = (e_y_5 >> 8) & 0xFF
            e_y_5_low = e_y_5 & 0xFF

            send_lst = [0x2c, 0x12, e_x_5_high, e_x_5_low, e_y_5_high, e_y_5_low, 0x5B]
            safe_uart_send(send_lst)

            # 检查激光发射条件
            if distance < 20:
                fire_laser()

            # 显示信息
            img.draw_string_advanced(10, 10, 16, f"HD矩形跟踪: ({rect_cx}, {rect_cy})", color=(0, 255, 0))
            img.draw_string_advanced(10, 30, 16, f"距离: {distance:.1f}px", color=(255, 255, 255))
    else:
        max_blue_blob = find_max_blob(blue_blobs)
        if max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            if rects:
                max_rect = find_max_rect(rects)
                if max_rect:
                    rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                    rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

                    e_x_6 = rect_cx - max_blue_blob.cx()
                    e_y_6 = rect_cy - max_blue_blob.cy()

                    e_x_6_high = (e_x_6 >> 8) & 0xFF
                    e_x_6_low = e_x_6 & 0xFF
                    e_y_6_high = (e_y_6 >> 8) & 0xFF
                    e_y_6_low = e_y_6 & 0xFF

                    send_lst = [0x2c, 0x12, e_x_6_high, e_x_6_low, e_y_6_high, e_y_6_low, 0x5B]
                    safe_uart_send(send_lst)

                    img.draw_string_advanced(10, 10, 16, "双目标相对跟踪 HD", color=(255, 255, 0))

    # 显示帧率
    fps = clock.fps()
    img.draw_string_advanced(10, 50, 16, f"FPS: {fps:.1f} HD", color=(255, 255, 255))

    Display.show_image(img)

#Flag==6 - 发挥部分第三题（已移除红色识别功能）
def goto_target_point_4():
    """发挥部分第三题 - 原红色识别功能已移除"""
    global rect_cx, rect_cy

    # 使用QVGA黑框识别替代
    use_qvga_detection = True

    if use_qvga_detection:
        # 使用QVGA黑框识别
        target_found, target_x, target_y, e_x_hd, e_y_hd = detect_black_frame_QVGA()

        if target_found:
            # 发送控制信号
            e_x_high = (e_x_hd >> 8) & 0xFF
            e_x_low = e_x_hd & 0xFF
            e_y_high = (e_y_hd >> 8) & 0xFF
            e_y_low = e_y_hd & 0xFF

            send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
            safe_uart_send(send_lst)

            # 检查激光发射条件
            distance = calculate_distance(target_x, target_y, 160, 120)
            if distance < 10:
                fire_laser()
        return

    # 备用HD方法
    sensor_HD()
    img = sensor.snapshot()
    img.draw_string_advanced(10, 10, 20, "发挥部分第三题", color=(255, 255, 255))
    img.draw_string_advanced(10, 35, 16, "原红色识别功能已移除", color=(255, 255, 0))
    img.draw_string_advanced(10, 55, 16, "当前使用黑框识别", color=(0, 255, 0))
    Display.show_image(img)

#Flag==7 - 系统监控
def goto_target_point_5():
    """系统状态监控"""
    img = sensor.snapshot()

    # 显示系统信息
    fps = clock.fps()
    gc.collect()
    free_mem = gc.mem_free()

    img.draw_rectangle(10, 10, 300, 150, color=(50, 50, 50), thickness=2, fill=True)
    img.draw_string_advanced(20, 20, 20, "系统监控", color=(255, 255, 255))
    img.draw_string_advanced(20, 50, 16, f"FPS: {fps:.1f}", color=(0, 255, 0))
    img.draw_string_advanced(20, 70, 16, f"内存: {free_mem//1024}KB", color=(255, 255, 0))
    img.draw_string_advanced(20, 90, 16, f"Flag: {Flag}", color=(255, 255, 255))
    img.draw_string_advanced(20, 110, 14, f"蓝色阈值: {blue_threshold}", color=(0, 0, 255))
    img.draw_string_advanced(20, 130, 14, f"二值化阈值: {binary_threshold}", color=(255, 255, 255))

    Display.show_image(img)

# 限制数值范围
def clamp(value, min_val, max_val):
    """限制数值在指定范围内"""
    return max(min_val, min(value, max_val))

#Flag==8 - 黑框白底目标检测
def goto_target_point_8():
    """黑框白底目标检测和跟踪"""
    global target_bw, view_offset_bw

    start_time = ticks_ms()
    img = sensor.snapshot()

    # 在指定ROI区域内查找黑色色块
    roi_x = clamp(200 + view_offset_bw[0], 0, 800 - roi_size_bw)
    roi_y = clamp(120 + view_offset_bw[1], 0, 480 - roi_size_bw)
    roi = (roi_x, roi_y, roi_size_bw, roi_size_bw)

    # 查找黑框
    black_blobs = img.find_blobs([thr_black], roi=roi, merge=False, pixels_threshold=100)

    target_found = False

    if black_blobs:
        for single_black_blob in black_blobs:
            # 检查黑色区域的密度比例
            blob_density = single_black_blob.pixels() / (single_black_blob.w() * single_black_blob.h())

            if blob_density < 0.3:  # 因为黑框内部不是黑色，所以这个比值不会很大
                # 在黑框区域内找白色
                white_blobs = img.find_blobs([thr_white],
                                           roi=single_black_blob.rect(),
                                           merge=False,
                                           area_threshold=2)

                if white_blobs:
                    largest_white = find_max_blob(white_blobs)

                    if largest_white:
                        # 判断条件1：黑色区域面积和白色区域面积的比例
                        # 判断条件2：黑框和白色区域中心坐标的差值
                        area_ratio = largest_white.pixels() / single_black_blob.pixels()
                        center_diff_x = abs(largest_white.cx() - single_black_blob.cx())
                        center_diff_y = abs(largest_white.cy() - single_black_blob.cy())

                        if (2 < area_ratio < 4) and center_diff_x < 10 and center_diff_y < 10:
                            # 计算目标坐标（白色区域中心坐标 + 补偿）
                            target_bw = [
                                largest_white.cx() + target_offset_bw[0],
                                largest_white.cy() + target_offset_bw[1]
                            ]

                            # 绘制目标十字线
                            img.draw_cross(target_bw[0], target_bw[1], color=(255, 0, 0), size=10, thickness=3)

                            # 绘制黑框和白色区域
                            img.draw_rectangle(single_black_blob.rect(), color=(0, 255, 255), thickness=2)
                            img.draw_rectangle(largest_white.rect(), color=(255, 255, 0), thickness=2)

                            target_found = True
                            break

    # 更新视角偏移补偿
    if target_found:
        # 计算相对于ROI中心的偏差
        roi_center_x = roi_x + roi_size_bw // 2
        roi_center_y = roi_y + roi_size_bw // 2

        view_offset_bw[0] += round((target_bw[0] - roi_center_x) * 0.5)
        view_offset_bw[1] += round((target_bw[1] - roi_center_y) * 0.5)

    # 限制视野补偿的最大、最小值
    view_offset_bw[0] = clamp(view_offset_bw[0], 0, 200)
    view_offset_bw[1] = clamp(view_offset_bw[1], 0, 120)

    # 计算包含视野补偿的偏差量（最终用于云台运动的量）
    err = [
        target_bw[0] + view_offset_bw[0] - 400,
        target_bw[1] + view_offset_bw[1] - 240
    ]

    # 绘制辅助线和偏差指示
    img.draw_line(0, 240, 800, 240, color=(100, 100, 100))  # 水平中心线
    img.draw_line(400, 0, 400, 480, color=(100, 100, 100))  # 垂直中心线

    # 绘制当前ROI区域
    img.draw_rectangle(roi, color=(0, 255, 0), thickness=2)

    # 绘制偏差指示圆点
    err_display_x = clamp(err[0] + 400, 0, 799)
    err_display_y = clamp(err[1] + 240, 0, 479)
    img.draw_circle(err_display_x, err_display_y, 8, color=(255, 0, 0), thickness=2, fill=True)

    # 发送控制信号给单片机
    if target_found:
        # 将偏差值转换为串口数据
        e_x = int(err[0])
        e_y = int(err[1])

        e_x_high = (e_x >> 8) & 0xFF
        e_x_low = e_x & 0xFF
        e_y_high = (e_y >> 8) & 0xFF
        e_y_low = e_y & 0xFF

        send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
        safe_uart_send(send_lst)

    # 显示调试信息
    process_time = ticks_ms() - start_time
    fps = clock.fps()

    img.draw_string_advanced(10, 10, 16, f"目标: {target_bw}", color=(255, 255, 255))
    img.draw_string_advanced(10, 30, 16, f"偏差: {err}", color=(255, 255, 255))
    img.draw_string_advanced(10, 50, 16, f"视角补偿: {view_offset_bw}", color=(255, 255, 255))
    img.draw_string_advanced(10, 70, 16, f"处理时间: {process_time}ms", color=(255, 255, 255))
    img.draw_string_advanced(10, 90, 16, f"FPS: {fps:.1f}", color=(255, 255, 255))

    # 显示目标状态
    status_text = "目标锁定" if target_found else "搜索目标"
    status_color = (0, 255, 0) if target_found else (255, 255, 0)
    img.draw_string_advanced(10, 110, 16, f"状态: {status_text}", color=status_color)

    Display.show_image(img)



#_________________________________________脱机调阈值系统函数_____________________________________________

# 阈值验证函数
def validate_threshold():
    """验证阈值的逻辑正确性，确保min值不大于max值"""
    if current_mode == 'lab':
        # 检查L_min <= L_max
        if lab_threshold[0] > lab_threshold[1]:
            lab_threshold[0] = lab_threshold[1]
        # 检查A_min <= A_max
        if lab_threshold[2] > lab_threshold[3]:
            lab_threshold[2] = lab_threshold[3]
        # 检查B_min <= B_max
        if lab_threshold[4] > lab_threshold[5]:
            lab_threshold[4] = lab_threshold[5]
    else:  # gray mode
        # 检查gray_min <= gray_max
        if gray_threshold[0] > gray_threshold[1]:
            gray_threshold[0] = gray_threshold[1]

# 阈值调整系统主函数
def threshold_adjustment_system():
    """
    脱机调阈值系统主函数 - 自动使用HD分辨率
    返回处理后的图像，由主程序负责显示
    """
    global current_mode, threshold_adjustment_mode, Flag, binary_threshold

    # 自动切换到HD分辨率进行阈值调节
    sensor_HD()

    # 捕获摄像头图像
    img_cam = sensor.snapshot()
    img_cam = img_cam.copy(roi=cut_roi)

    # 创建用于绘制按钮的画布
    img = sensor.snapshot()  # 使用完整的摄像头图像作为背景

    # 应用当前阈值到裁剪的图像
    if current_mode == 'gray':
        # 灰度图识别 - 二值化显示
        img_processed = img_cam.to_grayscale()
        img_processed = img_processed.binary([gray_threshold[:2]])
        img_processed = img_processed.to_rgb565()
        # 同时更新二值化阈值
        binary_threshold = gray_threshold[:2]
    else:  # 'lab'
        # LAB空间识别 - 二值化显示
        try:
            img_processed = img_cam.binary([lab_threshold])
            img_processed = img_processed.to_rgb565()
        except:
            img_processed = img_cam

    # 将处理后的图像绘制到中央
    center_x = (800 - img_processed.width()) // 2
    center_y = (480 - img_processed.height()) // 2
    img.draw_image(img_processed, center_x, center_y)

    # 绘制界面按钮
    button_color = (150, 150, 150)
    text_color = (0, 0, 0)

    # 返回按钮
    img.draw_rectangle(0, 0, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(50, 10, 20, "返回", color=text_color)

    # 切换按钮
    img.draw_rectangle(800-160, 0, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(800-160+50, 10, 20, "切换", color=text_color)

    # 归位按钮
    img.draw_rectangle(0, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(50, 480-30, 20, "归位", color=text_color)

    # 保存按钮
    img.draw_rectangle(800-160, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(800-160+50, 480-30, 20, "保存", color=text_color)

    # 左侧滑块按钮（减少值）
    button_labels_left = ["L-", "L-", "A-", "A-", "B-", "B-"] if current_mode == 'lab' else ["G-", "G-", "", "", "", ""]
    for i in range(6):
        y_pos = 60 + i * 60
        img.draw_rectangle(0, y_pos, 160, 40, color=button_color, thickness=2, fill=True)
        if button_labels_left[i]:  # 只显示有效按钮的标签
            img.draw_string_advanced(70, y_pos + 10, 20, button_labels_left[i], color=text_color)

    # 右侧滑块按钮（增加值）
    button_labels_right = ["L+", "L+", "A+", "A+", "B+", "B+"] if current_mode == 'lab' else ["G+", "G+", "", "", "", ""]
    for i in range(6):
        y_pos = 60 + i * 60
        img.draw_rectangle(800-160, y_pos, 160, 40, color=button_color, thickness=2, fill=True)
        if button_labels_right[i]:  # 只显示有效按钮的标签
            img.draw_string_advanced(800-160+70, y_pos + 10, 20, button_labels_right[i], color=text_color)

    # 显示当前模式提示
    img.draw_rectangle(300, 10, 200, 30, color=(200, 200, 200), thickness=2, fill=True)
    mode_text = "灰度模式" if current_mode == 'gray' else "LAB模式"
    img.draw_string_advanced(320, 15, 20, f"模式: {mode_text}", color=text_color)

    # 显示当前阈值值
    if current_mode == 'gray':
        img.draw_string_advanced(10, 420, 18,
                                 f"灰度阈值: [{gray_threshold[0]}, {gray_threshold[1]}]",
                                 color=text_color)
    else:
        img.draw_string_advanced(10, 420, 16,
                                 f"LAB阈值: L[{lab_threshold[0]},{lab_threshold[1]}] A[{lab_threshold[2]},{lab_threshold[3]}] B[{lab_threshold[4]},{lab_threshold[5]}]",
                                 color=text_color)

    # 处理触摸输入
    points = tp.read()
    if points:
        x, y = points[0].x, points[0].y

        # 判断按下的按钮
        def which_key(x, y):
            if x < 160:
                if y < 40: return "return"
                if y > 480-40: return "reset"
                if 60 <= y < 420: return str((y-60)//60)
            elif x > 800-160:
                if y < 40: return "change"
                if y > 480-40: return "save"
                if 60 <= y < 420: return str((y-60)//60+6)
            return None

        btn = which_key(x, y)
        if btn:
            # 返回按钮
            if btn == "return":
                threshold_adjustment_mode = False
                Flag = 0
                print("退出阈值调节模式，Flag重置为0")
                return img

            # 切换阈值模式
            elif btn == "change":
                if current_mode == 'lab':
                    current_mode = 'gray'
                    print("切换到灰度模式")
                else:
                    current_mode = 'lab'
                    print("切换到LAB模式")

            # 阈值归位
            elif btn == "reset":
                if current_mode == 'gray':
                    gray_threshold[0] = 0
                    gray_threshold[1] = 255
                    binary_threshold = gray_threshold[:2]
                    print("已重置灰度阈值为全白色背景")
                else:  # lab
                    lab_threshold[0] = 0
                    lab_threshold[1] = 100
                    lab_threshold[2] = -128
                    lab_threshold[3] = 127
                    lab_threshold[4] = -128
                    lab_threshold[5] = 127
                    print("已重置LAB阈值为全白色背景")

            # 保存当前阈值
            elif btn == "save":
                if current_mode == 'lab':
                    # LAB模式保存时需要调整A、B通道的值（减去128）
                    adj_vals = [lab_threshold[i] for i in range(6)]
                    saved_lab_thresholds.append(adj_vals.copy())
                    print(f"已保存LAB阈值: {adj_vals}")
                elif current_mode == 'gray':
                    saved_gray_thresholds.append(gray_threshold[:2].copy())
                    binary_threshold = gray_threshold[:2]
                    print(f"已保存灰度阈值: {gray_threshold[:2]}")

            # 调整滑块
            else:
                idx = int(btn)
                max_params = 2 if current_mode == 'gray' else 6

                if idx >= 6:  # 右侧按钮增加阈值
                    chan_idx = idx - 6
                    if chan_idx < max_params:
                        if current_mode == 'gray':
                            gray_threshold[chan_idx] = gray_threshold[chan_idx] + 2 # 修复：使用min而不是max
                            binary_threshold = gray_threshold[:2]
                        else:  # lab
                            if chan_idx < 2:  # L通道
                                lab_threshold[chan_idx] = lab_threshold[chan_idx] + 2
                            else:  # A, B通道
                                lab_threshold[chan_idx] = lab_threshold[chan_idx] + 2
                else:  # 左侧按钮减小阈值
                    chan_idx = idx
                    if chan_idx < max_params:
                        if current_mode == 'gray':
                            gray_threshold[chan_idx] = gray_threshold[chan_idx] - 2
                            binary_threshold = gray_threshold[:2]
                        else:  # lab
                            if chan_idx < 2:  # L通道
                                lab_threshold[chan_idx] = lab_threshold[chan_idx] - 2
                            else:  # A, B通道
                                lab_threshold[chan_idx] = lab_threshold[chan_idx] - 2

                # 验证阈值逻辑
                validate_threshold()

            time.sleep_ms(100)  # 防止重复触发

    return img

#_____________________________________________主程序________________________________________________
while True:
    try:
        clock.tick()
        os.exitpoint()

        # 串口数据处理
        if uart.any():
            Flag_transform()

        # 定期内存清理，提高性能
        if ticks_ms() % 5000 < 50:  # 每5秒清理一次内存
            gc.collect()

        # 性能优化：减少跳帧以提高检测精度
        frame_skip_count += 1
        if frame_skip_count <= max_frame_skip and Flag not in [1, 7]:
            continue
        frame_skip_count = 0

        # 性能优化：减少不必要的内存分配
        current_time = ticks_ms()
        if current_time - last_detection_time < detection_interval and Flag not in [1, 6, 7]:
            # 对于非关键任务，降低检测频率以提高帧率
            img = sensor.snapshot()
            Display.show_image(img)
            continue
        last_detection_time = current_time

        # 实现一个长按屏幕进入阈值编辑模式的效果（仅在非绘画模式下检测）
        if Flag != 6:  # 绘画模式下不检测触摸，避免干扰
            points = tp.read()
            if len(points) > 0:
                touch_counter += 1
                if touch_counter > 5:  # 减少触摸检测时间，提高响应速度
                    if not threshold_adjustment_mode:
                        threshold_adjustment_mode = True
                        Flag = 1
                        # 重置阈值为全白色背景的初始值
                        lab_threshold = [0, 100, -128, 127, -128, 127]  # LAB全范围，显示纯白色
                        gray_threshold = [0, 255]  # 灰度全范围，显示纯白色
                        current_mode = 'lab'  # 默认进入LAB模式
                        print("进入阈值调节模式 - 初始化为纯白色背景")
                        touch_counter = 0  # 重置计数器
                # 减少调试输出，提高性能
                # print(f"触摸位置: {points[0].x}, {points[0].y}")
            else:
                touch_counter -= 2
                touch_counter = max(0, touch_counter)

        # 主要功能逻辑
        if Flag == 0:
            img = sensor.snapshot()
            storage()
            Display.show_image(img) #显示图片

        elif Flag == 1:
            # 检查是否进入阈值调节模式
            if threshold_adjustment_mode:
                # 执行阈值调节系统（自动切换到HD分辨率）
                img = threshold_adjustment_system()
                Display.show_image(img)
            else:
                # QVGA黑框识别模式
                goto_target_point()

        #E题基本要求(2)：2秒内瞄准靶心，D1≤2cm
        elif Flag == 2:
            goto_target_point_1()

        #E题基本要求(3)：4秒内自动瞄准，D1≤2cm
        elif Flag == 3:
            goto_target_point_2()

        #E题发挥(1)：N=1圈，t≤20s，D1≤2cm
        elif Flag == 4:
            goto_target_point_3()

        #E题发挥(2)：N=2圈，t≤40s，D1≤2cm
        elif Flag == 5:
            goto_target_point_3()

        #发挥部分第三题（已移除红色识别功能）
        elif Flag == 6:
            goto_target_point_4()

        #系统监控
        elif Flag == 7:
            goto_target_point_5()

        #黑框白底检测（额外功能）
        elif Flag == 8:
            goto_target_point_8()

    except Exception as e:
        print(f"主程序错误: {e}")
        # 错误恢复：重置到安全状态
        Flag = 0
        threshold_adjustment_mode = False
        time.sleep_ms(100)

