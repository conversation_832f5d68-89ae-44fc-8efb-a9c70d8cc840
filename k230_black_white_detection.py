# K230版本 - 黑框白底目标检测和跟踪系统
# 改编自B站@程欢欢的智能控制集 OpenMV代码
# 适配01科技K230平台

import time
import os
import sys
import math
import gc

from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import UART
from machine import TOUCH

from media.sensor import *
from media.display import *
from media.media import *

#______________________________________变量定义并赋初值___________________________________________

# 任务标志位
Flag = 8  # 设置为8表示黑框白底检测模式

# 黑框和白底的颜色阈值，可以宽泛一些 (LAB色彩空间)
thr_white = (60, 100, -20, 20, -20, 20)  # 白色阈值
thr_black = (0, 35, -20, 20, -20, 20)    # 黑色阈值

# 目标检测相关变量
target_offset = [-1, -1]  # 目标补偿量，根据视角倾斜角度而设
target = [0, 0]           # 目标坐标，自动计算
view_offset = [0, 0]      # 视角偏移补偿量，自动计算
deviation = [0, 0]        # 偏差量，最终输出结果

# 图像处理参数
img_center_x = 400  # K230屏幕中心X (800/2)
img_center_y = 240  # K230屏幕中心Y (480/2)
roi_size = 240      # ROI区域大小

# 性能优化变量
last_process_time = 0
process_interval = 30  # 处理间隔(ms)

#_________________________________________模块初始化_____________________________________________

# 摄像头传感器初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width=800, height=480)
sensor.set_pixformat(Sensor.RGB565)

# 串口初始化
fpioa = FPIOA()
fpioa.set_function(3, FPIOA.UART1_TXD)
fpioa.set_function(4, FPIOA.UART1_RXD)

uart = UART(UART.UART1, 115200)

# 缓冲区和3.5寸的屏幕图像显示
Display.init(Display.ST7701, to_ide=True)

# 初始化媒体管理器
MediaManager.init()

# 启动摄像头传感器模块
sensor.run()

# 时钟模块建立
clock = time.clock()

# 建立触摸屏
tp = TOUCH(0)

print("K230黑框白底目标检测系统初始化完成")

#__________________________________________功能函数定义_____________________________________________

# 安全的串口发送函数
def safe_uart_send(data_list):
    """安全的串口发送函数，包含错误处理"""
    try:
        uart.write(bytes(data_list))
        return True
    except Exception as e:
        print(f"串口发送错误: {e}")
        return False

# 找到最大的色块
def find_max_blob(blobs):
    """找到面积最大的色块"""
    if not blobs:
        return None
    return max(blobs, key=lambda b: b.pixels())

# 限制数值范围
def clamp(value, min_val, max_val):
    """限制数值在指定范围内"""
    return max(min_val, min(value, max_val))

# 黑框白底目标检测主函数
def detect_black_white_target():
    """检测黑框白底目标"""
    global target, view_offset, deviation
    
    start_time = ticks_ms()
    img = sensor.snapshot()
    
    # 在指定ROI区域内查找黑色色块
    roi_x = clamp(200 + view_offset[0], 0, 800 - roi_size)
    roi_y = clamp(120 + view_offset[1], 0, 480 - roi_size)
    roi = (roi_x, roi_y, roi_size, roi_size)
    
    # 查找黑框
    black_blobs = img.find_blobs([thr_black], roi=roi, merge=False, pixels_threshold=100)
    
    target_found = False
    
    if black_blobs:
        for single_black_blob in black_blobs:
            # 绘制初步找黑框结果（调试用）
            # img.draw_rectangle(single_black_blob.rect(), color=(255, 0, 255))
            
            # 检查黑色区域的密度比例
            blob_density = single_black_blob.pixels() / (single_black_blob.w() * single_black_blob.h())
            
            if blob_density < 0.3:  # 因为黑框内部不是黑色，所以这个比值不会很大
                # img.draw_rectangle(single_black_blob.rect(), color=(0, 255, 255))  # 绘制符合条件的区域
                
                # 在黑框区域内找白色
                white_blobs = img.find_blobs([thr_white], 
                                           roi=single_black_blob.rect(), 
                                           merge=False, 
                                           area_threshold=2)
                
                if white_blobs:
                    largest_white = find_max_blob(white_blobs)
                    
                    if largest_white:
                        # 判断条件1：黑色区域面积和白色区域面积的比例
                        # 判断条件2：黑框和白色区域中心坐标的差值
                        area_ratio = largest_white.pixels() / single_black_blob.pixels()
                        center_diff_x = abs(largest_white.cx() - single_black_blob.cx())
                        center_diff_y = abs(largest_white.cy() - single_black_blob.cy())
                        
                        if (2 < area_ratio < 4) and center_diff_x < 10 and center_diff_y < 10:
                            # 计算目标坐标（白色区域中心坐标 + 补偿）
                            target = [
                                largest_white.cx() + target_offset[0], 
                                largest_white.cy() + target_offset[1]
                            ]
                            
                            # 绘制目标十字线
                            img.draw_cross(target[0], target[1], color=(255, 0, 0), size=10, thickness=3)
                            
                            # 绘制黑框和白色区域
                            img.draw_rectangle(single_black_blob.rect(), color=(0, 255, 255), thickness=2)
                            img.draw_rectangle(largest_white.rect(), color=(255, 255, 0), thickness=2)
                            
                            target_found = True
                            break
    
    # 更新视角偏移补偿
    if target_found:
        # 计算相对于ROI中心的偏差
        roi_center_x = roi_x + roi_size // 2
        roi_center_y = roi_y + roi_size // 2
        
        view_offset[0] += round((target[0] - roi_center_x) * 0.5)
        view_offset[1] += round((target[1] - roi_center_y) * 0.5)
    
    # 限制视野补偿的最大、最小值
    view_offset[0] = clamp(view_offset[0], 0, 200)
    view_offset[1] = clamp(view_offset[1], 0, 120)
    
    # 计算包含视野补偿的偏差量（最终用于云台运动的量）
    err = [
        target[0] + view_offset[0] - img_center_x,
        target[1] + view_offset[1] - img_center_y
    ]
    
    # 绘制辅助线和偏差指示
    img.draw_line(0, img_center_y, 800, img_center_y, color=(100, 100, 100))  # 水平中心线
    img.draw_line(img_center_x, 0, img_center_x, 480, color=(100, 100, 100))  # 垂直中心线
    
    # 绘制当前ROI区域
    img.draw_rectangle(roi, color=(0, 255, 0), thickness=2)
    
    # 绘制偏差指示圆点
    err_display_x = clamp(err[0] + img_center_x, 0, 799)
    err_display_y = clamp(err[1] + img_center_y, 0, 479)
    img.draw_circle(err_display_x, err_display_y, 8, color=(255, 0, 0), thickness=2, fill=True)
    
    # 发送控制信号给单片机
    if target_found:
        # 将偏差值转换为串口数据
        e_x = int(err[0])
        e_y = int(err[1])
        
        e_x_high = (e_x >> 8) & 0xFF
        e_x_low = e_x & 0xFF
        e_y_high = (e_y >> 8) & 0xFF
        e_y_low = e_y & 0xFF
        
        send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
        safe_uart_send(send_lst)
    
    # 显示调试信息
    process_time = ticks_ms() - start_time
    fps = clock.fps()
    
    img.draw_string_advanced(10, 10, 16, f"目标: {target}", color=(255, 255, 255))
    img.draw_string_advanced(10, 30, 16, f"偏差: {err}", color=(255, 255, 255))
    img.draw_string_advanced(10, 50, 16, f"视角补偿: {view_offset}", color=(255, 255, 255))
    img.draw_string_advanced(10, 70, 16, f"处理时间: {process_time}ms", color=(255, 255, 255))
    img.draw_string_advanced(10, 90, 16, f"FPS: {fps:.1f}", color=(255, 255, 255))
    
    # 显示目标状态
    status_text = "目标锁定" if target_found else "搜索目标"
    status_color = (0, 255, 0) if target_found else (255, 255, 0)
    img.draw_string_advanced(10, 110, 16, f"状态: {status_text}", color=status_color)
    
    Display.show_image(img)
    
    return target_found

#_____________________________________________主程序________________________________________________
while True:
    try:
        clock.tick()
        os.exitpoint()

        # 性能优化：控制处理频率
        current_time = ticks_ms()
        if current_time - last_process_time >= process_interval:
            last_process_time = current_time

            # 执行黑框白底目标检测
            if Flag == 8:
                detect_black_white_target()
            else:
                # 其他模式的简单图像显示
                img = sensor.snapshot()
                Display.show_image(img)

        # 定期内存清理
        if current_time % 5000 < 50:
            gc.collect()

    except Exception as e:
        print(f"主程序错误: {e}")
        time.sleep_ms(100)
