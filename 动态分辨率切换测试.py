# K230动态分辨率切换系统测试
# 演示QVGA高帧率检测 vs HD高精度阈值调节

import time
import os
import math
import gc
from time import ticks_ms
from machine import FPIOA, Pin, UART, TOUCH
from media.sensor import *
from media.display import *
from media.media import *

#______________________________________参数配置___________________________________________

# 任务模式选择
Flag = 9  # 1:HD阈值调节, 9:QVGA高帧率黑框检测

# 动态分辨率切换系统
current_resolution = "HD"  # 当前分辨率状态
resolution_switch_delay = 100  # 分辨率切换延迟(ms)
last_resolution_switch = 0
resolution_switching = False

# 检测阈值
black_threshold = (0, 30, -20, 20, -20, 20)  # 黑色阈值

#_________________________________________模块初始化_____________________________________________

# 摄像头初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width=800, height=480)
sensor.set_pixformat(Sensor.RGB565)

# 串口初始化
fpioa = FPIOA()
fpioa.set_function(3, FPIOA.UART1_TXD)
fpioa.set_function(4, FPIOA.UART1_RXD)
uart = UART(UART.UART1, 115200)

# 显示初始化
Display.init(Display.ST7701, to_ide=True)
MediaManager.init()
sensor.run()
clock = time.clock()
tp = TOUCH(0)

print("动态分辨率切换系统初始化完成")

#__________________________________________分辨率切换函数_____________________________________________

def switch_to_hd_resolution():
    """切换到HD分辨率 800x480"""
    global current_resolution, last_resolution_switch, resolution_switching
    
    if current_resolution == "HD":
        return True
    
    current_time = ticks_ms()
    if current_time - last_resolution_switch < resolution_switch_delay:
        return False
    
    try:
        resolution_switching = True
        print("切换到HD分辨率 800x480")
        
        sensor.stop()
        time.sleep_ms(50)
        
        sensor.reset()
        sensor.set_framesize(width=800, height=480)
        sensor.set_pixformat(Sensor.RGB565)
        
        sensor.run()
        time.sleep_ms(100)
        
        current_resolution = "HD"
        last_resolution_switch = current_time
        resolution_switching = False
        
        print("HD分辨率切换完成")
        return True
        
    except Exception as e:
        print(f"HD分辨率切换失败: {e}")
        resolution_switching = False
        return False

def switch_to_qvga_resolution():
    """切换到QVGA分辨率 320x240"""
    global current_resolution, last_resolution_switch, resolution_switching
    
    if current_resolution == "QVGA":
        return True
    
    current_time = ticks_ms()
    if current_time - last_resolution_switch < resolution_switch_delay:
        return False
    
    try:
        resolution_switching = True
        print("切换到QVGA分辨率 320x240")
        
        sensor.stop()
        time.sleep_ms(50)
        
        sensor.reset()
        sensor.set_framesize(width=320, height=240)
        sensor.set_pixformat(Sensor.RGB565)
        
        sensor.run()
        time.sleep_ms(100)
        
        current_resolution = "QVGA"
        last_resolution_switch = current_time
        resolution_switching = False
        
        print("QVGA分辨率切换完成")
        return True
        
    except Exception as e:
        print(f"QVGA分辨率切换失败: {e}")
        resolution_switching = False
        return False

def get_resolution_info():
    """获取当前分辨率信息"""
    if current_resolution == "HD":
        return 800, 480, "HD"
    else:
        return 320, 240, "QVGA"

#__________________________________________功能函数_____________________________________________

def safe_uart_send(data_list):
    """安全的串口发送"""
    try:
        uart.write(bytes(data_list))
        return True
    except:
        return False

def hd_threshold_adjustment():
    """HD分辨率阈值调节"""
    if not switch_to_hd_resolution():
        img = sensor.snapshot()
        img.draw_string_advanced(10, 10, 24, "切换到HD中...", color=(255, 255, 0))
        Display.show_image(img)
        return
    
    img = sensor.snapshot()
    width, height, res_name = get_resolution_info()
    
    # 显示原图
    img.draw_string_advanced(10, 10, 20, f"HD阈值调节 {width}x{height}", color=(255, 255, 255))
    
    # 查找黑色区域用于阈值调节
    black_blobs = img.find_blobs([black_threshold], merge=False, pixels_threshold=100)
    
    if black_blobs:
        for i, blob in enumerate(black_blobs[:3]):
            img.draw_rectangle(blob.rect(), color=(255, 0, 0), thickness=2)
            img.draw_string_advanced(blob.x(), blob.y()-15, 12, f"黑色{i+1}", color=(255, 0, 0))
    
    # 显示性能信息
    fps = clock.fps()
    img.draw_string_advanced(10, 40, 16, f"FPS: {fps:.1f}", color=(255, 255, 255))
    img.draw_string_advanced(10, 60, 16, f"检测到 {len(black_blobs) if black_blobs else 0} 个黑色区域", color=(255, 255, 255))
    img.draw_string_advanced(10, 80, 16, "高精度阈值调节模式", color=(0, 255, 0))
    
    Display.show_image(img)

def qvga_high_fps_detection():
    """QVGA高帧率黑框检测"""
    if not switch_to_qvga_resolution():
        img = sensor.snapshot()
        img.draw_string_advanced(10, 10, 20, "切换到QVGA中...", color=(255, 255, 0))
        Display.show_image(img)
        return
    
    img = sensor.snapshot()
    width, height, res_name = get_resolution_info()
    center_x = width // 2
    center_y = height // 2
    
    # QVGA下的高帧率检测
    black_blobs = img.find_blobs([black_threshold], merge=False, pixels_threshold=30)
    
    target_count = 0
    detected_targets = []
    
    if black_blobs:
        black_blobs.sort(key=lambda b: b.pixels(), reverse=True)
        
        for blob in black_blobs[:4]:
            if 40 <= blob.pixels() <= 8000:  # QVGA适配的面积范围
                aspect_ratio = blob.w() / blob.h() if blob.h() > 0 else 0
                if 0.2 <= aspect_ratio <= 5.0:
                    target_count += 1
                    detected_targets.append(blob)
                    
                    # 绘制检测结果
                    img.draw_rectangle(blob.rect(), color=(255, 0, 0), thickness=2)
                    img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 0), size=6, thickness=2)
                    img.draw_string_advanced(blob.x(), blob.y()-8, 8, f"T{target_count}", color=(255, 0, 0))
                    
                    if target_count >= 2:
                        break
    
    # 双目标跟踪
    if target_count >= 2:
        t1, t2 = detected_targets[0], detected_targets[1]
        center_x_track = (t1.cx() + t2.cx()) // 2
        center_y_track = (t1.cy() + t2.cy()) // 2
        
        # 绘制跟踪信息
        img.draw_cross(center_x_track, center_y_track, color=(0, 255, 0), size=8, thickness=2)
        img.draw_line(t1.cx(), t1.cy(), t2.cx(), t2.cy(), color=(0, 255, 0), thickness=2)
        
        # 计算误差
        e_x = center_x - center_x_track
        e_y = center_y - center_y_track
        
        # 发送控制信号（模拟）
        if abs(e_x) > 3 or abs(e_y) > 3:
            # 这里可以发送实际的控制指令
            pass
        
        img.draw_string_advanced(5, 5, 10, f"跟踪中 误差:({e_x},{e_y})", color=(0, 255, 0))
    else:
        img.draw_string_advanced(5, 5, 12, f"搜索目标... 找到{target_count}个", color=(255, 255, 0))
    
    # 显示性能信息
    fps = clock.fps()
    img.draw_string_advanced(5, height-25, 10, f"{res_name} {width}x{height}", color=(255, 255, 255))
    img.draw_string_advanced(5, height-12, 10, f"FPS: {fps:.1f}", color=(255, 255, 255))
    
    # 绘制中心十字线
    img.draw_cross(center_x, center_y, color=(0, 0, 255), size=6, thickness=1)
    
    Display.show_image(img)

#_____________________________________________主程序________________________________________________
while True:
    try:
        clock.tick()
        os.exitpoint()
        
        if Flag == 1:
            # HD分辨率阈值调节
            hd_threshold_adjustment()
        elif Flag == 9:
            # QVGA高帧率检测
            qvga_high_fps_detection()
        else:
            # 默认显示
            img = sensor.snapshot()
            width, height, res_name = get_resolution_info()
            img.draw_string_advanced(10, 10, 20, f"动态分辨率切换测试", color=(255, 255, 255))
            img.draw_string_advanced(10, 35, 16, f"当前: {res_name} {width}x{height}", color=(255, 255, 255))
            img.draw_string_advanced(10, 55, 16, f"Flag=1: HD阈值调节", color=(255, 255, 0))
            img.draw_string_advanced(10, 75, 16, f"Flag=9: QVGA高帧率检测", color=(255, 255, 0))
            img.draw_string_advanced(10, 95, 16, f"当前Flag={Flag}", color=(0, 255, 0))
            
            fps = clock.fps()
            img.draw_string_advanced(10, 115, 16, f"FPS: {fps:.1f}", color=(255, 255, 255))
            
            Display.show_image(img)
        
        # 定期内存清理
        if ticks_ms() % 5000 < 50:
            gc.collect()
            
    except Exception as e:
        print(f"程序错误: {e}")
        time.sleep_ms(100)
