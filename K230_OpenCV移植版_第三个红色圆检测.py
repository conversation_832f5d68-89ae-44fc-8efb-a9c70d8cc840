# K230平台 - OpenCV算法移植版第三个红色圆检测
# 将OpenCV的霍夫圆检测算法思想移植到K230平台

import time
import os
import math
import gc
from time import ticks_ms
from machine import FPIOA, Pin, UART, TOUCH
from media.sensor import *
from media.display import *
from media.media import *

#______________________________________核心参数配置___________________________________________

# 任务模式
Flag = 6  # 专门用于第三个红色圆检测

# 分辨率控制
current_resolution = "HD"

# 颜色阈值优化（针对红色圆圈）
red_threshold = (25, 100, 10, 127, 10, 127)  # 优化的红色阈值

# 第三个红色圆检测参数
third_circle_params = {
    'min_radius': 20,      # 最小半径
    'max_radius': 80,      # 最大半径
    'min_area': 300,       # 最小面积
    'max_area': 6000,      # 最大面积
    'circularity_min': 0.3, # 最小圆形度
    'center_tolerance': 25,  # 同心圆中心容差
    'red_ratio_min': 0.2    # 最小红色像素比例
}

# 检测状态
detected_circles = []
target_circle = None
concentric_groups = []

#_________________________________________模块初始化_____________________________________________

# 摄像头初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width=800, height=480)
sensor.set_pixformat(Sensor.RGB565)

# 串口初始化
fpioa = FPIOA()
fpioa.set_function(3, FPIOA.UART1_TXD)
fpioa.set_function(4, FPIOA.UART1_RXD)
uart = UART(UART.UART1, 115200)

# 显示初始化
Display.init(Display.ST7701, to_ide=True)
MediaManager.init()
sensor.run()
clock = time.clock()

print("K230 OpenCV移植版第三个红色圆检测系统启动")

#__________________________________________核心算法函数_____________________________________________

def safe_uart_send(data_list):
    """安全串口发送"""
    try:
        uart.write(bytes(data_list))
        return True
    except:
        return False

def send_tracking_command(e_x, e_y):
    """发送跟踪控制指令"""
    e_x_high = (e_x >> 8) & 0xFF
    e_x_low = e_x & 0xFF
    e_y_high = (e_y >> 8) & 0xFF
    e_y_low = e_y & 0xFF
    
    track_cmd = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
    return safe_uart_send(track_cmd)

def calculate_distance(x1, y1, x2, y2):
    """计算两点间距离"""
    return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)

def calculate_circularity(blob):
    """计算圆形度"""
    area = blob.pixels()
    perimeter = blob.perimeter()
    
    if perimeter == 0:
        return 0
    
    circularity = (4 * math.pi * area) / (perimeter * perimeter)
    return circularity

def filter_red_circles(img):
    """筛选红色圆形区域 - 模拟OpenCV的红色筛选"""
    try:
        # 检测所有红色区域
        red_blobs = img.find_blobs([red_threshold], 
                                  pixels_threshold=20, 
                                  area_threshold=third_circle_params['min_area'])
        
        if not red_blobs:
            return []
        
        # 筛选圆形特征
        circle_candidates = []
        
        for blob in red_blobs:
            area = blob.pixels()
            
            # 面积筛选
            if third_circle_params['min_area'] <= area <= third_circle_params['max_area']:
                # 计算等效半径
                radius = math.sqrt(area / math.pi)
                
                # 半径筛选
                if third_circle_params['min_radius'] <= radius <= third_circle_params['max_radius']:
                    # 圆形度筛选
                    circularity = calculate_circularity(blob)
                    
                    if circularity >= third_circle_params['circularity_min']:
                        # 计算长宽比（额外的圆形验证）
                        aspect_ratio = blob.w() / blob.h() if blob.h() > 0 else 0
                        
                        # 圆形的长宽比应该接近1
                        if 0.6 <= aspect_ratio <= 1.4:
                            circle_candidates.append({
                                'blob': blob,
                                'center': (blob.cx(), blob.cy()),
                                'radius': radius,
                                'area': area,
                                'circularity': circularity,
                                'aspect_ratio': aspect_ratio
                            })
        
        return circle_candidates
        
    except Exception as e:
        print(f"红色圆形筛选错误: {e}")
        return []

def detect_concentric_circles(circle_candidates):
    """检测同心圆组 - 模拟OpenCV的同心圆检测"""
    try:
        if len(circle_candidates) < 2:
            return []
        
        # 按距离分组寻找同心圆
        concentric_groups = []
        used_circles = set()
        
        for i, circle1 in enumerate(circle_candidates):
            if i in used_circles:
                continue
                
            group = [circle1]
            used_circles.add(i)
            
            center1 = circle1['center']
            
            # 寻找与circle1同心的其他圆
            for j, circle2 in enumerate(circle_candidates):
                if j in used_circles or i == j:
                    continue
                
                center2 = circle2['center']
                
                # 计算圆心距离
                center_distance = calculate_distance(center1[0], center1[1], 
                                                   center2[0], center2[1])
                
                # 如果圆心距离小于容差，认为是同心圆
                if center_distance <= third_circle_params['center_tolerance']:
                    group.append(circle2)
                    used_circles.add(j)
            
            # 只保留至少有2个圆的组
            if len(group) >= 2:
                # 按半径排序
                group.sort(key=lambda x: x['radius'])
                concentric_groups.append(group)
        
        return concentric_groups
        
    except Exception as e:
        print(f"同心圆检测错误: {e}")
        return []

def find_third_circle(concentric_groups, circle_candidates):
    """寻找第三个红色圆 - 模拟OpenCV的目标选择"""
    try:
        best_third_circle = None
        best_score = 0
        best_group = None
        
        # 策略1: 在同心圆组中寻找第三个圆
        for group in concentric_groups:
            if len(group) >= 3:
                # 第三个圆（按半径排序后的索引2）
                third_circle = group[2]
                
                # 评分：圆形度 + 半径合理性 + 位置合理性
                circularity_score = third_circle['circularity']
                
                # 半径评分（理想半径范围）
                radius = third_circle['radius']
                if 30 <= radius <= 50:
                    radius_score = 1.0
                elif 25 <= radius <= 60:
                    radius_score = 0.8
                else:
                    radius_score = 0.6
                
                # 位置评分（不应该太靠边）
                center_x, center_y = third_circle['center']
                edge_distance = min(center_x, center_y, 800-center_x, 480-center_y)
                position_score = 1.0 if edge_distance > 50 else 0.7
                
                # 综合评分
                total_score = circularity_score * radius_score * position_score
                
                if total_score > best_score:
                    best_score = total_score
                    best_third_circle = third_circle
                    best_group = group
        
        # 策略2: 如果没有找到同心圆组，选择最符合特征的单个圆
        if best_third_circle is None and circle_candidates:
            for circle in circle_candidates:
                radius = circle['radius']
                circularity = circle['circularity']
                
                # 第三个圆的典型特征
                if 25 <= radius <= 60 and circularity > 0.4:
                    # 简单评分
                    score = circularity * (1.0 if 30 <= radius <= 50 else 0.8)
                    
                    if score > best_score:
                        best_score = score
                        best_third_circle = circle
                        best_group = [circle]
        
        return best_third_circle, best_group
        
    except Exception as e:
        print(f"第三个圆寻找错误: {e}")
        return None, None

def opencv_style_third_circle_detection(img):
    """OpenCV风格的第三个红色圆检测主函数"""
    global detected_circles, target_circle, concentric_groups
    
    try:
        # 步骤1: 筛选红色圆形区域
        circle_candidates = filter_red_circles(img)
        detected_circles = circle_candidates
        
        if not circle_candidates:
            target_circle = None
            concentric_groups = []
            return False, 0, 0, None
        
        # 步骤2: 检测同心圆组
        concentric_groups = detect_concentric_circles(circle_candidates)
        
        # 步骤3: 寻找第三个圆
        third_circle, best_group = find_third_circle(concentric_groups, circle_candidates)
        
        if third_circle:
            target_circle = third_circle
            blob = third_circle['blob']
            return True, blob.cx(), blob.cy(), blob
        
        target_circle = None
        return False, 0, 0, None
        
    except Exception as e:
        print(f"OpenCV风格检测错误: {e}")
        target_circle = None
        return False, 0, 0, None

#__________________________________________主要任务函数_____________________________________________

def main_detection_task():
    """主检测任务"""
    global target_circle
    
    img = sensor.snapshot()
    width, height = 800, 480
    center_x, center_y = width // 2, height // 2
    
    # 使用OpenCV风格检测第三个红色圆
    found, rx, ry, circle_blob = opencv_style_third_circle_detection(img)
    
    if found and target_circle:
        # 绘制目标圆
        img.draw_rectangle(circle_blob.rect(), color=(255, 0, 0), thickness=3)
        img.draw_circle(rx, ry, 8, color=(255, 0, 0), thickness=3, fill=True)
        
        # 绘制圆形轮廓
        radius = int(target_circle['radius'])
        img.draw_circle(rx, ry, radius, color=(0, 255, 255), thickness=3)
        
        # 绘制同心圆组
        if concentric_groups:
            for group in concentric_groups:
                if len(group) >= 3:
                    for i, circle in enumerate(group):
                        cx, cy = circle['center']
                        r = int(circle['radius'])
                        
                        if i == 0:
                            color = (255, 0, 0)  # 蓝色
                            label = "1st"
                        elif i == 1:
                            color = (0, 255, 0)  # 绿色
                            label = "2nd"
                        elif i == 2:
                            color = (0, 0, 255)  # 红色
                            label = "3rd"
                        else:
                            color = (255, 255, 0)
                            label = f"{i+1}th"
                        
                        img.draw_circle(cx, cy, r, color, thickness=2)
                        img.draw_string_advanced(cx-20, cy-r-15, 10, label, color=color)
        
        # 计算跟踪误差并发送
        track_error_x = center_x - rx
        track_error_y = center_y - ry
        send_tracking_command(track_error_x, track_error_y)
        
        # 显示信息
        img.draw_string_advanced(10, 10, 20, "第三个红色圆检测成功!", color=(0, 255, 0))
        img.draw_string_advanced(10, 35, 16, f"中心: ({rx}, {ry})", color=(255, 0, 0))
        img.draw_string_advanced(10, 55, 16, f"半径: {target_circle['radius']:.1f}px", color=(255, 0, 0))
        img.draw_string_advanced(10, 75, 16, f"圆形度: {target_circle['circularity']:.3f}", color=(255, 0, 0))
        img.draw_string_advanced(10, 95, 16, f"跟踪误差: ({track_error_x}, {track_error_y})", color=(255, 255, 255))
        
        if concentric_groups:
            img.draw_string_advanced(10, 115, 16, f"同心圆组: {len(concentric_groups)}个", color=(0, 255, 255))
        
        # 绘制跟踪线
        img.draw_line(center_x, center_y, rx, ry, color=(255, 255, 0), thickness=2)
        
    else:
        # 显示调试信息
        img.draw_string_advanced(10, 10, 20, "搜索第三个红色圆...", color=(255, 255, 0))
        img.draw_string_advanced(10, 35, 16, f"检测到 {len(detected_circles)} 个圆形候选", color=(255, 255, 0))
        img.draw_string_advanced(10, 55, 16, f"同心圆组: {len(concentric_groups)}个", color=(255, 255, 0))
        
        # 绘制所有候选圆
        for i, circle in enumerate(detected_circles[:6]):
            blob = circle['blob']
            img.draw_rectangle(blob.rect(), color=(255, 255, 0), thickness=1)
            
            cx, cy = circle['center']
            r = int(circle['radius'])
            img.draw_circle(cx, cy, r, color=(255, 255, 0), thickness=1)
            
            info = f"R:{r}"
            img.draw_string_advanced(blob.x(), blob.y()-15, 8, info, color=(255, 255, 0))
    
    # 绘制中心十字线
    img.draw_cross(center_x, center_y, color=(0, 0, 255), size=15, thickness=2)
    
    # 显示系统信息
    fps = clock.fps()
    img.draw_string_advanced(10, 140, 16, f"FPS: {fps:.1f}", color=(255, 255, 255))
    img.draw_string_advanced(10, 160, 16, f"OpenCV移植版算法", color=(255, 255, 255))
    
    Display.show_image(img)

#_____________________________________________主程序________________________________________________
while True:
    try:
        clock.tick()
        os.exitpoint()
        
        main_detection_task()
        
        # 定期内存清理
        if ticks_ms() % 5000 < 50:
            gc.collect()
            
    except Exception as e:
        print(f"程序错误: {e}")
        time.sleep_ms(100)
