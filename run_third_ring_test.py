#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三红环检测快速启动脚本
"""

import os
import sys

def main():
    print("=" * 60)
    print("五个红环检测功能测试（第三环高亮）")
    print("=" * 60)
    print("请选择运行模式:")
    print("1. 运行主程序（完整功能）")
    print("2. 运行五个红环专门测试")
    print("3. 查看功能说明")
    print("4. 退出")
    print("=" * 60)
    
    while True:
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == '1':
            print("启动主程序...")
            try:
                os.system('python "树莓派opencv识别代码.py"')
            except Exception as e:
                print(f"启动失败: {e}")
            break
            
        elif choice == '2':
            print("启动五个红环专门测试...")
            try:
                os.system('python test_third_ring.py')
            except Exception as e:
                print(f"启动失败: {e}")
            break

        elif choice == '3':
            print("打开功能说明文档...")
            try:
                if os.name == 'nt':  # Windows
                    os.system('notepad "五个红环检测说明.md"')
                else:  # Linux/Mac
                    os.system('cat "五个红环检测说明.md"')
            except Exception as e:
                print(f"打开文档失败: {e}")
            break
            
        elif choice == '4':
            print("退出程序")
            break
            
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
