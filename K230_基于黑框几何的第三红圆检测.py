# K230平台基于黑框几何关系的第三个红色圆检测系统
# 利用题目给定的几何尺寸，通过黑框位置精确预估第三个红色圆位置

import time
import os
import math
import gc
from time import ticks_ms
from machine import FPIOA, Pin, UART, TOUCH
from media.sensor import *
from media.display import *
from media.media import *

#______________________________________核心参数配置___________________________________________

# 任务模式
Flag = 6  # 专门用于第三个红色圆检测

# 分辨率配置
CAPTURE_WIDTH = 800  # 使用HD分辨率提高精度
CAPTURE_HEIGHT = 480
current_resolution = "HD"

# 题目几何参数（基于A4纸和题目要求）
geometry_params = {
    # A4纸尺寸：210mm x 297mm
    'a4_width_mm': 210,     # A4纸宽度(mm)
    'a4_height_mm': 297,    # A4纸长度(mm)
    'black_tape_width_mm': 18,  # 黑色胶带宽度1.8cm
    
    # 红色圆半径（题目要求：2,4,6,8,10cm）
    'circle_radii_mm': [20, 40, 60, 80, 100],  # 转换为mm
    'target_circle_index': 2,  # 第三个圆（索引2，半径6cm=60mm）
    
    # 有效检测区域（去除黑色胶带后的区域）
    'effective_width_mm': 210 - 2*18,   # 174mm
    'effective_height_mm': 297 - 2*18,  # 261mm
}

# 颜色阈值
red_threshold = (25, 100, 10, 127, 10, 127)  # 红色阈值
binary_threshold = (45, 255)  # 黑框检测阈值

# 系统状态
black_frame_detected = False
black_frame_center = (0, 0)
black_frame_size = (0, 0)
third_circle_predicted = False
third_circle_center = (0, 0)
third_circle_radius = 0
pixel_to_mm_ratio = 1.0

frame_count = 0
fps = 0.0
last_fps_time = 0

#_________________________________________模块初始化_____________________________________________

# 摄像头初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width=CAPTURE_WIDTH, height=CAPTURE_HEIGHT)
sensor.set_pixformat(Sensor.RGB565)

# 串口初始化
fpioa = FPIOA()
fpioa.set_function(3, FPIOA.UART1_TXD)
fpioa.set_function(4, FPIOA.UART1_RXD)
uart = UART(UART.UART1, 115200)

# 显示初始化
Display.init(Display.ST7701, to_ide=True)
MediaManager.init()
sensor.run()
clock = time.clock()

print("K230基于黑框几何的第三个红色圆检测系统启动")

#__________________________________________核心算法函数_____________________________________________

def safe_uart_send(data_list):
    """安全串口发送"""
    try:
        uart.write(bytes(data_list))
        return True
    except:
        return False

def send_tracking_command(e_x, e_y):
    """发送跟踪控制指令"""
    e_x_high = (e_x >> 8) & 0xFF
    e_x_low = e_x & 0xFF
    e_y_high = (e_y >> 8) & 0xFF
    e_y_low = e_y & 0xFF
    
    track_cmd = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
    return safe_uart_send(track_cmd)

def calculate_distance(x1, y1, x2, y2):
    """计算两点间距离"""
    return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)

def find_max_rect(rects):
    """找到最大矩形"""
    if not rects:
        return None
    max_size = 0
    max_rect = None
    for rect in rects:
        if rect.w() * rect.h() > max_size:
            max_rect = rect
            max_size = rect.w() * rect.h()
    return max_rect

def detect_black_frame(img):
    """检测黑色边框并计算几何参数"""
    global black_frame_detected, black_frame_center, black_frame_size, pixel_to_mm_ratio
    
    try:
        # 转换到灰度图像进行矩形检测
        img_gray = img.to_grayscale(copy=True)
        
        # 应用二值化增强边缘
        img_binary = img_gray.binary([binary_threshold])
        rects = img_binary.find_rects(threshold=10000)
        
        if rects:
            max_rect = find_max_rect(rects)
            if max_rect:
                # 计算黑框中心
                corners = max_rect.corners()
                center_x = int(sum(p[0] for p in corners) / 4)
                center_y = int(sum(p[1] for p in corners) / 4)
                
                # 计算黑框尺寸
                width = max_rect.w()
                height = max_rect.h()
                
                # 更新全局状态
                black_frame_detected = True
                black_frame_center = (center_x, center_y)
                black_frame_size = (width, height)
                
                # 计算像素到毫米的转换比例
                # 假设检测到的是内边框（去除胶带宽度后的有效区域）
                pixel_to_mm_ratio_x = geometry_params['effective_width_mm'] / width
                pixel_to_mm_ratio_y = geometry_params['effective_height_mm'] / height
                pixel_to_mm_ratio = (pixel_to_mm_ratio_x + pixel_to_mm_ratio_y) / 2
                
                return True, max_rect, center_x, center_y, width, height
        
        black_frame_detected = False
        return False, None, 0, 0, 0, 0
        
    except Exception as e:
        print(f"黑框检测错误: {e}")
        black_frame_detected = False
        return False, None, 0, 0, 0, 0

def predict_third_circle_position():
    """基于黑框几何关系预测第三个红色圆的位置"""
    global third_circle_predicted, third_circle_center, third_circle_radius
    
    if not black_frame_detected:
        third_circle_predicted = False
        return False, 0, 0, 0
    
    try:
        # 第三个圆的半径（6cm = 60mm）
        target_radius_mm = geometry_params['circle_radii_mm'][geometry_params['target_circle_index']]
        
        # 转换为像素半径
        third_circle_radius = int(target_radius_mm / pixel_to_mm_ratio)
        
        # 第三个圆的中心就是黑框的中心（靶心位置）
        third_circle_center = black_frame_center
        
        third_circle_predicted = True
        
        return True, third_circle_center[0], third_circle_center[1], third_circle_radius
        
    except Exception as e:
        print(f"第三个圆位置预测错误: {e}")
        third_circle_predicted = False
        return False, 0, 0, 0

def verify_third_circle_in_predicted_area(img, pred_x, pred_y, pred_radius):
    """在预测区域内验证第三个红色圆"""
    try:
        # 定义搜索区域（预测位置周围的区域）
        search_margin = int(pred_radius * 0.3)  # 搜索边界为预测半径的30%
        
        # 计算搜索区域边界
        search_x1 = max(0, pred_x - pred_radius - search_margin)
        search_y1 = max(0, pred_y - pred_radius - search_margin)
        search_x2 = min(CAPTURE_WIDTH, pred_x + pred_radius + search_margin)
        search_y2 = min(CAPTURE_HEIGHT, pred_y + pred_radius + search_margin)
        
        # 在搜索区域内检测红色区域
        search_roi = (search_x1, search_y1, search_x2 - search_x1, search_y2 - search_y1)
        img_roi = img.copy(roi=search_roi)
        
        red_blobs = img_roi.find_blobs([red_threshold], pixels_threshold=20, area_threshold=100)
        
        if red_blobs:
            # 寻找最接近预测位置和半径的红色区域
            best_match = None
            best_score = 0
            
            for blob in red_blobs:
                # 计算实际位置（相对于原图）
                actual_x = blob.cx() + search_x1
                actual_y = blob.cy() + search_y1
                
                # 计算等效半径
                area = blob.pixels()
                radius = math.sqrt(area / math.pi)
                
                # 计算圆形度
                perimeter = blob.perimeter()
                circularity = (4 * math.pi * area) / (perimeter * perimeter) if perimeter > 0 else 0
                
                # 位置匹配度
                position_error = calculate_distance(actual_x, actual_y, pred_x, pred_y)
                position_score = max(0, 1 - position_error / (pred_radius * 0.5))
                
                # 半径匹配度
                radius_error = abs(radius - pred_radius)
                radius_score = max(0, 1 - radius_error / (pred_radius * 0.3))
                
                # 圆形度评分
                circularity_score = min(circularity * 2, 1.0)
                
                # 综合评分
                total_score = position_score * radius_score * circularity_score
                
                if total_score > best_score and total_score > 0.5:  # 最低匹配阈值
                    best_score = total_score
                    best_match = {
                        'blob': blob,
                        'actual_x': actual_x,
                        'actual_y': actual_y,
                        'radius': radius,
                        'circularity': circularity,
                        'score': total_score,
                        'search_offset': (search_x1, search_y1)
                    }
            
            if best_match:
                return True, best_match
        
        return False, None
        
    except Exception as e:
        print(f"第三个圆验证错误: {e}")
        return False, None

def draw_detection_results(img):
    """绘制检测结果"""
    global fps, frame_count, last_fps_time
    
    # 计算FPS
    current_time = ticks_ms()
    frame_count += 1
    
    if current_time - last_fps_time > 1000:  # 每秒更新一次FPS
        fps = frame_count * 1000.0 / (current_time - last_fps_time)
        frame_count = 0
        last_fps_time = current_time
    
    # 绘制黑框检测结果
    if black_frame_detected:
        center_x, center_y = black_frame_center
        width, height = black_frame_size
        
        # 绘制黑框中心
        img.draw_circle(center_x, center_y, 5, color=(0, 255, 0), thickness=2, fill=True)
        img.draw_cross(center_x, center_y, color=(0, 255, 0), size=15, thickness=2)
        
        # 显示黑框信息
        img.draw_string_advanced(10, 10, 16, f"黑框中心: ({center_x}, {center_y})", color=(0, 255, 0))
        img.draw_string_advanced(10, 30, 16, f"黑框尺寸: {width}x{height}", color=(0, 255, 0))
        img.draw_string_advanced(10, 50, 16, f"像素比例: {pixel_to_mm_ratio:.2f}mm/px", color=(0, 255, 0))
    
    # 绘制第三个圆预测结果
    if third_circle_predicted:
        pred_x, pred_y = third_circle_center
        pred_radius = third_circle_radius
        
        # 绘制预测圆圈（虚线效果）
        for i in range(0, 360, 30):  # 每30度画一段
            angle1 = math.radians(i)
            angle2 = math.radians(i + 15)
            x1 = int(pred_x + pred_radius * math.cos(angle1))
            y1 = int(pred_y + pred_radius * math.sin(angle1))
            x2 = int(pred_x + pred_radius * math.cos(angle2))
            y2 = int(pred_y + pred_radius * math.sin(angle2))
            img.draw_line(x1, y1, x2, y2, color=(255, 255, 0), thickness=2)
        
        # 绘制预测中心
        img.draw_circle(pred_x, pred_y, 3, color=(255, 255, 0), thickness=2, fill=True)
        
        # 在预测区域内验证第三个圆
        verified, match_result = verify_third_circle_in_predicted_area(img, pred_x, pred_y, pred_radius)
        
        if verified and match_result:
            # 绘制验证成功的第三个圆
            actual_x = match_result['actual_x']
            actual_y = match_result['actual_y']
            actual_radius = int(match_result['radius'])
            
            # 绘制实际检测到的圆（红色）
            img.draw_circle(actual_x, actual_y, actual_radius, color=(255, 0, 0), thickness=3)
            img.draw_circle(actual_x, actual_y, 5, color=(255, 0, 0), thickness=3, fill=True)
            
            # 发送跟踪指令
            track_error_x = CAPTURE_WIDTH // 2 - actual_x
            track_error_y = CAPTURE_HEIGHT // 2 - actual_y
            send_tracking_command(track_error_x, track_error_y)
            
            # 显示成功信息
            img.draw_string_advanced(10, 70, 16, f"第三个圆检测成功!", color=(255, 0, 0))
            img.draw_string_advanced(10, 90, 14, f"实际位置: ({actual_x}, {actual_y})", color=(255, 0, 0))
            img.draw_string_advanced(10, 110, 14, f"实际半径: {actual_radius}px", color=(255, 0, 0))
            img.draw_string_advanced(10, 130, 14, f"匹配度: {match_result['score']:.3f}", color=(255, 0, 0))
            img.draw_string_advanced(10, 150, 14, f"跟踪误差: ({track_error_x}, {track_error_y})", color=(255, 255, 255))
            
            # 绘制跟踪线
            img.draw_line(CAPTURE_WIDTH // 2, CAPTURE_HEIGHT // 2, actual_x, actual_y, 
                         color=(0, 0, 255), thickness=2)
        else:
            img.draw_string_advanced(10, 70, 16, f"预测位置: ({pred_x}, {pred_y})", color=(255, 255, 0))
            img.draw_string_advanced(10, 90, 16, f"预测半径: {pred_radius}px", color=(255, 255, 0))
            img.draw_string_advanced(10, 110, 16, "在预测区域搜索中...", color=(255, 255, 0))
    
    # 显示系统信息
    img.draw_string_advanced(10, 180, 14, f"FPS: {fps:.1f}", color=(255, 255, 255))
    img.draw_string_advanced(10, 200, 14, "基于黑框几何预测", color=(0, 255, 255))
    
    # 绘制屏幕中心十字线
    center_x = CAPTURE_WIDTH // 2
    center_y = CAPTURE_HEIGHT // 2
    img.draw_cross(center_x, center_y, color=(0, 0, 255), size=20, thickness=2)

#__________________________________________主要任务函数_____________________________________________

def main_geometric_detection():
    """基于几何关系的主检测任务"""
    img = sensor.snapshot()
    
    # 1. 检测黑色边框
    frame_detected, max_rect, center_x, center_y, width, height = detect_black_frame(img)
    
    if frame_detected:
        # 2. 基于黑框几何关系预测第三个圆位置
        circle_predicted, pred_x, pred_y, pred_radius = predict_third_circle_position()
        
        # 3. 绘制检测结果
        draw_detection_results(img)
        
        # 绘制黑框轮廓
        if max_rect:
            corners = max_rect.corners()
            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(0, 255, 0), thickness=2)
    else:
        img.draw_string_advanced(10, 10, 20, "搜索黑框中...", color=(255, 255, 0))
        img.draw_string_advanced(10, 40, 16, "请确保黑框完整可见", color=(255, 255, 0))
    
    # 显示图像
    Display.show_image(img)
    
    return frame_detected and third_circle_predicted

#_____________________________________________主程序________________________________________________
print("开始基于黑框几何的第三个红色圆检测...")
print("系统参数:")
print(f"- 分辨率: {CAPTURE_WIDTH}x{CAPTURE_HEIGHT}")
print(f"- 目标圆半径: {geometry_params['circle_radii_mm'][geometry_params['target_circle_index']]}mm (第三个圆)")
print(f"- A4纸有效区域: {geometry_params['effective_width_mm']}x{geometry_params['effective_height_mm']}mm")

while True:
    try:
        clock.tick()
        os.exitpoint()
        
        # 执行基于几何关系的检测
        detection_success = main_geometric_detection()
        
        # 定期内存清理
        if ticks_ms() % 5000 < 50:
            gc.collect()
            
    except Exception as e:
        print(f"程序错误: {e}")
        time.sleep_ms(100)
