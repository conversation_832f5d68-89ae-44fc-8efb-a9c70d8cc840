# K230黑框识别算法
# 基于OpenMV算法思路转换而来
# 算法原理：先识别黑框，再在黑框内寻找白色区域，通过两者的关系确定目标

import time
import os
import sys
import math
import gc

from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import UART
from machine import RTC
from machine import ADC
from machine import TOUCH

from media.sensor import *
from media.display import *
from media.media import *

#______________________________________变量定义并赋初值___________________________________________

# 颜色阈值定义 - 根据K230的LAB色彩空间调整
# 白底阈值 (L, A, B)
thr_white = (60, 100, -20, 20, -20, 20)
# 黑框阈值 (L, A, B) 
thr_black = (0, 35, -20, 20, -20, 20)

# 目标跟踪变量
target_offset = [-1, -1]  # 目标补偿量，根据视角倾斜角度设置
target = [0, 0]           # 目标坐标，自动计算
view_offset = [0, 0]      # 视角偏移补偿量，自动计算
deviation = [0, 0]        # 偏差量，最终输出结果

# 图像尺寸参数
img_width = 400
img_height = 240
center_x = img_width // 2
center_y = img_height // 2

# 识别参数
black_area_ratio_threshold = 0.3  # 黑框内部填充比例阈值
white_black_ratio_min = 2         # 白色/黑色面积比例最小值
white_black_ratio_max = 4         # 白色/黑色面积比例最大值
center_diff_threshold = 10        # 黑框和白色区域中心差值阈值

#_________________________________________模块初始化_____________________________________________

# 摄像头传感器初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width=img_width, height=img_height)
sensor.set_pixformat(Sensor.RGB565)

# 串口初始化
fpioa = FPIOA()
fpioa.set_function(3, FPIOA.UART1_TXD)
fpioa.set_function(4, FPIOA.UART1_RXD)
uart = UART(UART.UART1, 115200)

# LED初始化
fpioa.set_function(52, FPIOA.GPIO52)
LED = Pin(52, Pin.OUT)

# 缓冲区和屏幕图像显示
Display.init(Display.ST7701, to_ide=True)

# 初始化媒体管理器
MediaManager.init()

# 启动摄像头传感器模块
sensor.run()

# 时钟模块建立
clock = time.clock()

# 建立触摸屏
tp = TOUCH(0)

#_________________________________________核心算法函数_____________________________________________

def find_largest_blob(blobs):
    """找到面积最大的色块"""
    if not blobs:
        return None
    return max(blobs, key=lambda b: b.w() * b.h())

def calculate_blob_fill_ratio(blob):
    """计算色块的填充比例"""
    if blob.w() * blob.h() == 0:
        return 0
    return blob.pixels() / (blob.w() * blob.h())

def detect_black_frame_target():
    """
    黑框目标检测主函数
    算法流程：
    1. 寻找黑色区域
    2. 筛选符合条件的黑框（填充比例低）
    3. 在黑框内寻找白色区域
    4. 验证黑框和白色区域的关系
    5. 确定最终目标位置
    """
    global target, target_offset
    
    img = sensor.snapshot()
    target_found = False
    
    # 转换为LAB色彩空间进行颜色识别
    img_lab = img.to_lab(copy=True)
    
    # 第一步：寻找黑色区域
    black_blobs = img_lab.find_blobs([thr_black], merge=False, area_threshold=100)
    
    if black_blobs:
        for single_black_blob in black_blobs:
            # 绘制初步找到的黑框结果（调试用）
            # img.draw_rectangle(single_black_blob.rect(), color=(255, 0, 255))
            
            # 第二步：筛选符合条件的黑框
            # 黑框内部不是完全黑色，所以填充比例不会很大
            fill_ratio = calculate_blob_fill_ratio(single_black_blob)
            
            if fill_ratio < black_area_ratio_threshold:
                # 绘制符合条件的黑框区域（调试用）
                # img.draw_rectangle(single_black_blob.rect(), color=(0, 255, 255))
                
                # 第三步：在黑框区域内寻找白色区域
                roi = single_black_blob.rect()
                white_blobs = img_lab.find_blobs([thr_white], 
                                                area_threshold=2, 
                                                roi=roi, 
                                                merge=False)
                
                if white_blobs:
                    # 找到最大的白色区域
                    largest_white = find_largest_blob(white_blobs)
                    
                    if largest_white:
                        # 绘制识别到的白色区域（调试用）
                        # img.draw_rectangle(largest_white.rect(), color=(255, 0, 0))
                        
                        # 第四步：验证黑框和白色区域的关系
                        # 条件1：白色区域和黑色区域面积比例
                        area_ratio = largest_white.pixels() / single_black_blob.pixels()
                        
                        # 条件2：黑框和白色区域中心坐标差值
                        center_diff_x = abs(largest_white.cx() - single_black_blob.cx())
                        center_diff_y = abs(largest_white.cy() - single_black_blob.cy())
                        
                        # 第五步：确定最终目标
                        if (white_black_ratio_min < area_ratio < white_black_ratio_max and
                            center_diff_x < center_diff_threshold and
                            center_diff_y < center_diff_threshold):
                            
                            # 计算目标坐标（白色区域中心 + 补偿）
                            target = [largest_white.cx() + target_offset[0], 
                                     largest_white.cy() + target_offset[1]]
                            
                            # 绘制目标十字线
                            img.draw_cross(target[0], target[1], color=(255, 0, 0), size=10, thickness=3)
                            
                            target_found = True
                            break
    
    return img, target_found

def calculate_deviation():
    """
    计算偏差量和视角补偿
    实现自适应视角跟踪功能
    """
    global view_offset, target, center_x, center_y
    
    if target[0] != 0 or target[1] != 0:  # 如果检测到目标
        # 偏差量累加到视野补偿中
        view_offset[0] += round((target[0] - center_x) * 0.5)
        view_offset[1] += round((target[1] - center_y) * 0.5)
        
        # 限制视野补偿的最大、最小值
        view_offset[0] = min(200, max(0, view_offset[0]))
        view_offset[1] = min(120, max(0, view_offset[1]))
    
    # 包含视野补偿的偏差量（最终用于控制的量）
    # 注意：画布的Y轴与直角坐标系的Y轴相反
    err = [target[0] + view_offset[0] - center_x, 
           target[1] + view_offset[1] - center_y]
    
    return err

def draw_ui_elements(img, err):
    """绘制用户界面元素"""
    # 绘制中心十字线
    img.draw_line(0, center_y, img_width, center_y, color=(255, 255, 255))
    img.draw_line(center_x, 0, center_x, img_height, color=(255, 255, 255))
    
    # 绘制偏差量指示圆点
    err_x = err[0] + center_x
    err_y = err[1] + center_y
    
    # 确保绘制点在图像范围内
    err_x = max(5, min(img_width - 5, err_x))
    err_y = max(5, min(img_height - 5, err_y))
    
    img.draw_circle(err_x, err_y, 5, color=(255, 0, 0), thickness=2, fill=True)
    
    # 显示数值信息
    info_text = f"Target:({target[0]},{target[1]}) Err:({err[0]},{err[1]})"
    img.draw_string(5, 5, info_text, color=(255, 255, 255), scale=1)

def print_debug_info(timer_ms, fps):
    """打印调试信息"""
    print(f"用时: {timer_ms}ms, 实时帧速: {1000/timer_ms:.1f}fps, 平均帧速: {fps:.1f}fps")
    print(f"目标: {target}, 视角补偿: {view_offset}")

#_________________________________________主循环_____________________________________________

def main():
    """主函数"""
    print("=" * 50)
    print("K230黑框识别算法启动")
    print("算法特点：")
    print("1. 先识别黑框，再寻找内部白色区域")
    print("2. 通过面积比例和中心位置关系验证目标")
    print("3. 自适应视角补偿跟踪")
    print("=" * 50)
    
    while True:
        try:
            timer_start = ticks_ms()
            clock.tick()
            os.exitpoint()
            
            # 核心检测算法
            img, target_found = detect_black_frame_target()
            
            # 计算偏差量
            err = calculate_deviation()
            
            # 绘制UI元素
            draw_ui_elements(img, err)
            
            # 显示图像
            Display.show_image(img)
            
            # 计算处理时间
            timer_ms = ticks_diff(ticks_ms(), timer_start)
            
            # 打印调试信息（可选）
            if timer_ms > 0:
                print_debug_info(timer_ms, clock.fps())
            
            # 检查触摸屏输入（用于参数调整）
            touch_point = tp.read()
            if touch_point:
                x, y = touch_point
                # 可以在这里添加触摸调整参数的功能
                pass
                
        except Exception as e:
            print(f"错误: {e}")
            # 错误处理
            img = sensor.snapshot()
            img.draw_string(10, 10, f"Error: {e}", color=(255, 0, 0), scale=2)
            Display.show_image(img)
            time.sleep(1)

if __name__ == "__main__":
    main()
