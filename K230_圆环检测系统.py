# K230平台圆环检测系统 - 发挥部分第三题专用
# 从树莓派OpenCV代码移植到K230平台

import time
import os
import math
import gc
from time import ticks_ms
from machine import FPIOA, Pin, UART, TOUCH
from media.sensor import *
from media.display import *
from media.media import *

#______________________________________核心参数配置___________________________________________

# 任务模式
Flag = 6  # 专门用于圆环检测

# 分辨率配置
CAPTURE_WIDTH = 640
CAPTURE_HEIGHT = 480
current_resolution = "HD"

# 检测参数（可调节）
detection_params = {
    'param1': 100,          # 边缘检测高阈值
    'param2': 30,           # 累加器阈值
    'min_outer': 20,        # 外圆最小半径
    'max_outer': 100,       # 外圆最大半径
    'min_inner': 10,        # 内圆最小半径
    'max_inner': 80,        # 内圆最大半径
    'center_tolerance': 0.3, # 圆心距离容差
    'radius_ratio_min': 0.3, # 最小半径比例
    'radius_ratio_max': 0.8  # 最大半径比例
}

# 颜色阈值
red_threshold = (25, 100, 10, 127, 10, 127)  # 红色阈值

# 系统状态
detected_rings = []
frame_count = 0
fps = 0.0
last_fps_time = 0

#_________________________________________模块初始化_____________________________________________

# 摄像头初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width=CAPTURE_WIDTH, height=CAPTURE_HEIGHT)
sensor.set_pixformat(Sensor.RGB565)

# 串口初始化
fpioa = FPIOA()
fpioa.set_function(3, FPIOA.UART1_TXD)
fpioa.set_function(4, FPIOA.UART1_RXD)
uart = UART(UART.UART1, 115200)

# 显示初始化
Display.init(Display.ST7701, to_ide=True)
MediaManager.init()
sensor.run()
clock = time.clock()
tp = TOUCH(0)

print("K230圆环检测系统启动")

#__________________________________________核心算法函数_____________________________________________

def safe_uart_send(data_list):
    """安全串口发送"""
    try:
        uart.write(bytes(data_list))
        return True
    except:
        return False

def send_tracking_command(e_x, e_y):
    """发送跟踪控制指令"""
    e_x_high = (e_x >> 8) & 0xFF
    e_x_low = e_x & 0xFF
    e_y_high = (e_y >> 8) & 0xFF
    e_y_low = e_y & 0xFF
    
    track_cmd = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
    return safe_uart_send(track_cmd)

def calculate_distance(x1, y1, x2, y2):
    """计算两点间距离"""
    return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)

def calculate_circularity(blob):
    """计算圆形度"""
    area = blob.pixels()
    perimeter = blob.perimeter()
    
    if perimeter == 0:
        return 0
    
    circularity = (4 * math.pi * area) / (perimeter * perimeter)
    return circularity

def detect_circles_by_color_and_shape(img, min_radius, max_radius):
    """基于颜色和形状检测圆形 - 替代霍夫圆检测"""
    try:
        # 检测红色区域
        red_blobs = img.find_blobs([red_threshold], 
                                  pixels_threshold=20, 
                                  area_threshold=100)
        
        if not red_blobs:
            return []
        
        circles = []
        
        for blob in red_blobs:
            # 计算等效半径
            area = blob.pixels()
            radius = math.sqrt(area / math.pi)
            
            # 半径筛选
            if min_radius <= radius <= max_radius:
                # 圆形度筛选
                circularity = calculate_circularity(blob)
                
                if circularity > 0.3:  # 圆形度阈值
                    # 长宽比筛选
                    aspect_ratio = blob.w() / blob.h() if blob.h() > 0 else 0
                    
                    if 0.6 <= aspect_ratio <= 1.4:  # 接近正圆
                        circles.append({
                            'center': (blob.cx(), blob.cy()),
                            'radius': radius,
                            'area': area,
                            'circularity': circularity,
                            'blob': blob
                        })
        
        return circles
        
    except Exception as e:
        print(f"圆形检测错误: {e}")
        return []

def detect_ring_structures(img):
    """检测圆环结构 - 模拟OpenCV的圆环匹配"""
    global detected_rings
    
    try:
        # 检测外圆
        outer_circles = detect_circles_by_color_and_shape(
            img, 
            detection_params['min_outer'], 
            detection_params['max_outer']
        )
        
        # 检测内圆
        inner_circles = detect_circles_by_color_and_shape(
            img, 
            detection_params['min_inner'], 
            detection_params['max_inner']
        )
        
        rings = []
        
        if outer_circles and inner_circles:
            # 圆环匹配算法
            for outer in outer_circles:
                ox, oy = outer['center']
                oradius = outer['radius']
                
                for inner in inner_circles:
                    ix, iy = inner['center']
                    iradius = inner['radius']
                    
                    # 计算圆心距离
                    center_dist = calculate_distance(ox, oy, ix, iy)
                    
                    # 计算半径比例
                    radius_ratio = iradius / oradius if oradius > 0 else 0
                    
                    # 圆环匹配条件
                    max_center_dist = min(oradius, iradius) * detection_params['center_tolerance']
                    
                    if (center_dist < max_center_dist and 
                        detection_params['radius_ratio_min'] < radius_ratio < detection_params['radius_ratio_max']):
                        
                        # 计算圆环质量评分
                        center_score = 1.0 - (center_dist / max_center_dist)
                        ratio_score = 1.0 - abs(radius_ratio - 0.55) / 0.25  # 理想比例0.55
                        circularity_score = (outer['circularity'] + inner['circularity']) / 2
                        
                        quality_score = center_score * ratio_score * circularity_score
                        
                        rings.append({
                            'outer': outer,
                            'inner': inner,
                            'center_dist': center_dist,
                            'radius_ratio': radius_ratio,
                            'quality_score': quality_score
                        })
        
        # 按质量评分排序，选择最好的圆环
        rings.sort(key=lambda x: x['quality_score'], reverse=True)
        
        detected_rings = rings[:3]  # 保留前3个最好的圆环
        
        return detected_rings
        
    except Exception as e:
        print(f"圆环检测错误: {e}")
        detected_rings = []
        return []

def adjust_detection_params():
    """动态调整检测参数 - 模拟滑动条功能"""
    global detection_params
    
    # 根据检测结果动态调整参数
    if len(detected_rings) == 0:
        # 没有检测到圆环，放宽条件
        detection_params['param2'] = max(20, detection_params['param2'] - 2)
        detection_params['center_tolerance'] = min(0.5, detection_params['center_tolerance'] + 0.05)
    elif len(detected_rings) > 5:
        # 检测到太多圆环，收紧条件
        detection_params['param2'] = min(50, detection_params['param2'] + 2)
        detection_params['center_tolerance'] = max(0.2, detection_params['center_tolerance'] - 0.05)

def draw_detection_results(img):
    """绘制检测结果"""
    global fps, frame_count, last_fps_time
    
    # 计算FPS
    current_time = ticks_ms()
    frame_count += 1
    
    if current_time - last_fps_time > 1000:  # 每秒更新一次FPS
        fps = frame_count * 1000.0 / (current_time - last_fps_time)
        frame_count = 0
        last_fps_time = current_time
    
    # 绘制检测到的圆环
    for i, ring in enumerate(detected_rings):
        outer = ring['outer']
        inner = ring['inner']
        
        # 绘制外圆（红色）
        ox, oy = outer['center']
        oradius = int(outer['radius'])
        img.draw_circle(ox, oy, oradius, color=(255, 0, 0), thickness=3)
        
        # 绘制内圆（绿色）
        ix, iy = inner['center']
        iradius = int(inner['radius'])
        img.draw_circle(ix, iy, iradius, color=(0, 255, 0), thickness=2)
        
        # 绘制圆心连线
        img.draw_line(ox, oy, ix, iy, color=(255, 255, 0), thickness=1)
        
        # 显示圆环信息
        info_text = f"Ring{i+1}: R{oradius}"
        img.draw_string_advanced(ox - oradius, oy - oradius - 20, 12, info_text, color=(255, 0, 0))
        
        # 显示质量评分
        score_text = f"Q:{ring['quality_score']:.2f}"
        img.draw_string_advanced(ox - 30, oy + oradius + 10, 10, score_text, color=(0, 255, 255))
        
        # 如果是最佳圆环，发送跟踪指令
        if i == 0:  # 质量最高的圆环
            center_x = CAPTURE_WIDTH // 2
            center_y = CAPTURE_HEIGHT // 2
            
            track_error_x = center_x - ox
            track_error_y = center_y - oy
            
            send_tracking_command(track_error_x, track_error_y)
            
            # 绘制跟踪线
            img.draw_line(center_x, center_y, ox, oy, color=(0, 0, 255), thickness=2)
            img.draw_cross(ox, oy, color=(0, 0, 255), size=15, thickness=3)
    
    # 显示系统信息
    img.draw_string_advanced(10, 10, 16, f"FPS: {fps:.1f}", color=(255, 255, 0))
    img.draw_string_advanced(10, 30, 16, f"Rings: {len(detected_rings)}", color=(255, 255, 0))
    img.draw_string_advanced(10, 50, 16, f"P1:{detection_params['param1']} P2:{detection_params['param2']}", color=(255, 255, 0))
    img.draw_string_advanced(10, 70, 16, "K230 Ring Detection", color=(0, 255, 0))
    
    # 显示参数信息
    img.draw_string_advanced(10, 90, 12, f"OuterR: {detection_params['min_outer']}-{detection_params['max_outer']}", color=(255, 255, 255))
    img.draw_string_advanced(10, 105, 12, f"InnerR: {detection_params['min_inner']}-{detection_params['max_inner']}", color=(255, 255, 255))
    img.draw_string_advanced(10, 120, 12, f"Tolerance: {detection_params['center_tolerance']:.2f}", color=(255, 255, 255))
    
    # 绘制屏幕中心十字线
    center_x = CAPTURE_WIDTH // 2
    center_y = CAPTURE_HEIGHT // 2
    img.draw_cross(center_x, center_y, color=(0, 0, 255), size=20, thickness=2)

#__________________________________________主要任务函数_____________________________________________

def main_ring_detection():
    """主圆环检测任务"""
    img = sensor.snapshot()
    
    # 检测圆环结构
    rings = detect_ring_structures(img)
    
    # 动态调整参数
    adjust_detection_params()
    
    # 绘制结果
    draw_detection_results(img)
    
    # 显示图像
    Display.show_image(img)
    
    return len(rings) > 0

#_____________________________________________主程序________________________________________________
print("开始圆环检测...")
print("系统参数:")
print(f"- 分辨率: {CAPTURE_WIDTH}x{CAPTURE_HEIGHT}")
print(f"- 外圆半径范围: {detection_params['min_outer']}-{detection_params['max_outer']}")
print(f"- 内圆半径范围: {detection_params['min_inner']}-{detection_params['max_inner']}")

while True:
    try:
        clock.tick()
        os.exitpoint()
        
        # 执行圆环检测
        detection_success = main_ring_detection()
        
        # 定期内存清理
        if ticks_ms() % 5000 < 50:
            gc.collect()
            
    except Exception as e:
        print(f"程序错误: {e}")
        time.sleep_ms(100)
