import time
import os
import sys
import math
import random
import gc

from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import UART
from machine import RTC
from machine import ADC
from machine import TOUCH

from media.sensor import *
from media.display import *
from media.media import *

#______________________________________变量定义并赋初值___________________________________________

#任务标志位，通过串口接受数据改变
Flag = 2

# Flag = 0   正常拍照
# Flag = 1   脱机调整阈值并且存储
# Flag = 2   基础部分第二题
# Flag = 3   基础部分第三题
# Flag = 4   发挥部分第一题
# Flag = 5   发挥部分第二题
# Flag = 6   发挥部分第三题
# Flag = 7   其他部分

# 新增：性能优化变量
max_frame_skip = 0  # 不跳帧，提高帧率
frame_skip_count = 0  # 跳帧计数

# 性能优化：减少不必要的计算
last_detection_time = 0
detection_interval = 50  # 检测间隔(ms)

# 脱机调阈值系统的全局变量
threshold_adjustment_mode = False  # 是否进入阈值调节模式
current_mode = 'lab'  # 当前工作模式：'lab' 或 'gray'
lab_threshold = [0, 100, -128, 127, -128, 127]  # LAB阈值
gray_threshold = [0, 255]  # 灰度阈值
saved_lab_thresholds = []  # 保存的LAB阈值列表
saved_gray_thresholds = []  # 保存的灰度阈值列表

#颜色阈值
blue_threshold = (30, 50, -50, -15, -10, 30)
red_threshold = (30, 100, 15, 127, 15, 127)  # 红色圆检测阈值
binary_threshold = (45, 255)

#矩形中心的坐标
rect_cx = None
rect_cy = None

#屏幕中心
target_2_cx = 400
target_2_cy = 240

#触摸次数
touch_counter = 0

# 图像裁剪ROI（用于阈值调节）
cut_roi = (160, 120, 480, 240)

#_________________________________________模块初始化_____________________________________________

#摄像头传感器初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width = 800, height = 480)
sensor.set_pixformat(Sensor.RGB565)

#串口初始化
fpioa = FPIOA()
# UART1代码
fpioa.set_function(3,FPIOA.UART1_TXD)
fpioa.set_function(4,FPIOA.UART1_RXD)

uart=UART(UART.UART1,115200) #设置串口号1和波特率

#fpioa.set_function(52,FPIOA.GPIO52)
#LED=Pin(52,Pin.OUT) #构建led对象，GPIO52,输出

#缓冲区和3.5寸的屏幕图像显示
Display.init(Display.ST7701, to_ide=True)

# 初始化媒体管理器
MediaManager.init()

#启动摄像头传感器模块
sensor.run()

#时钟模块建立
clock = time.clock()

#开启计时器
rtc = machine.RTC()

#建立触摸屏
tp = TOUCH(0)

#_________________________________________kalerman函数_____________________________________________
class Vector:
    def __init__(self, lis):
        self._values = lis

    @classmethod
    def zero(cls, dim):
        return cls([0] * dim)

    def norm(self):
        return math.sqrt(sum(e**2 for e in self))

    def normalize(self):
        if self.norm() < 1e-8:
            raise ZeroDivisionError("Normalize error! norm is zero.")
        return 1 / self.norm() * Vector(self._values)

    def __add__(self, other):
        assert len(self) == len(other), "Vector length must be same"
        return Vector([a + b for a, b in zip(self, other)])

    def __sub__(self, other):
        assert len(self) == len(other), "Vector length must be same"
        return Vector([a - b for a, b in zip(self, other)])

    def dot(self, other):
        assert len(self) == len(other), "Vector length must be same."
        return sum(a * b for a,b in zip(self, other))

    def __mul__(self, k):
        return Vector([a * k for a in self])

    def __rmul__(self, k):
        return self * k

    def __truediv__(self, k):
        return 1/k * self

    def __pos__(self):
        return 1*self

    def __neg__(self):
        return -1*self

    def __getitem__(self, index):
        return self._values[index]

    def __len__(self):
        return len(self._values)

    def __repr__(self):
        return "Vector({})".format(self._values)

    def __str__(self):
        return "({})".format(",".join(str(e) for e in self._values))

class Matrix:
    def __init__(self, list2d):
        self._values = [row[:] for row in list2d]

    @classmethod
    def zero(cls, r, c):
        return cls([[0] * c for _ in range(r)])

    def T(self):
        return Matrix([[e for e in self.col_vector(i)]
                      for i in range(self.col_num())])

    def __add__(self, another):
        assert self.shape() == another.shape(), "Matrix shape must be same."
        return Matrix([[a + b for a, b in zip(self.row_vector(i), another.row_vector(i))]
                      for i in range(self.row_num())])

    def __sub__(self, another):
        assert self.shape() == another.shape(), "Matrix shape must be same."
        return Matrix([[a - b for a, b in zip(self.row_vector(i), another.row_vector(i))]
                      for i in range(self.row_num())])

    def dot(self, another):
        if isinstance(another, Vector):
            assert self.col_num() == len(another), "Error in Matrix-Vector Multiplication."
            return Vector([self.row_vector(i).dot(another) for i in range(self.row_num())])

        if isinstance(another, Matrix):
            assert self.col_num() == another.row_num(), "Error in Matrix-Matrix Multiplication."
            return Matrix([[self.row_vector(i).dot(another.col_vector(j)) for j in range(another.col_num())]
                          for i in range(self.row_num())])

    def __mul__(self, k):
        return Matrix([[e * k for e in self.row_vector(i)]
                      for i in range(self.row_num())])

    def __rmul__(self, k):
        return self * k

    def __truediv__(self, k):
        return (1 / k) * self

    def __pos__(self):
        return 1 * self

    def __neg__(self):
        return -1 * self

    def row_vector(self, index):
        return Vector(self._values[index])

    def col_vector(self, index):
        return Vector([row[index] for row in self._values])

    def __getitem__(self, pos):
        r, c = pos
        return self._values[r][c]

    def ni_matrix(self):
        """返回矩阵的逆（二维矩阵）"""
        a = self._values[0][0]
        b = self._values[0][1]
        c = self._values[1][0]
        d = self._values[1][1]
        det = a * d - b * c
        if abs(det) < 1e-10:  # 防止行列式接近零
            return Matrix([[1, 0], [0, 1]])
        ni_mat = Matrix([[d, -b], [-c, a]])
        return (1/det) * ni_mat

    def row_num(self):
        return len(self._values)

    def col_num(self):
        return len(self._values[0])

    def shape(self):
        return self.row_num(), self.col_num()

    def __repr__(self):
        return "Matrix({})".format(self._values)

#________________________________卡尔曼滤波器模型___________________________________________
# 噪声协方差矩阵
R = Matrix([[0.1, 0], [0, 0.1]])  # 测量噪声协方差
Q = Matrix([[0.1, 0], [0, 0.1]])  # 过程噪声协方差

# 滤波器状态变量
Xk_1hat = Matrix([[0], [0]])  # X方向状态
Yk_1hat = Matrix([[0], [0]])  # Y方向状态
XPk_1 = Matrix([[1, 0], [0, 1]])  # X方向误差协方差
YPk_1 = Matrix([[1, 0], [0, 1]])  # Y方向误差协方差

# 常数矩阵
I = Matrix([[1, 0], [0, 1]])  # 单位矩阵
H = Matrix([[1, 0], [0, 1]])  # 观测矩阵

#________________________________卡尔曼滤波器函数定义___________________________________________
def Kalman_x_filter(cx, vx):
    global A, I, H, Q, R, Xk_1hat, XPk_1

    # 预测步骤
    pre_Xkhat = A.dot(Xk_1hat)
    pre_Pk = (A.dot(XPk_1)).dot(A.T()) + Q

    # 卡尔曼增益
    Kk = pre_Pk.dot((pre_Pk + R).ni_matrix())

    # 更新步骤
    Zk = Matrix([[cx], [vx]])
    Xk_hat = pre_Xkhat + Kk.dot(Zk - H.dot(pre_Xkhat))
    Pk_1 = (I - Kk.dot(H)).dot(pre_Pk)

    # 更新状态
    XPk_1 = Pk_1
    Xk_1hat = Xk_hat

    return Xk_hat[0,0], Xk_hat[1,0]

def Kalman_y_filter(cy, vy):
    global A, I, H, Q, R, Yk_1hat, YPk_1

    # 预测步骤
    pre_Ykhat = A.dot(Yk_1hat)
    pre_Pk = (A.dot(YPk_1)).dot(A.T()) + Q

    # 卡尔曼增益
    Kk = pre_Pk.dot((pre_Pk + R).ni_matrix())

    # 更新步骤
    Zk = Matrix([[cy], [vy]])
    Yk_hat = pre_Ykhat + Kk.dot(Zk - H.dot(pre_Ykhat))
    Pk_1 = (I - Kk.dot(H)).dot(pre_Pk)

    # 更新状态
    YPk_1 = Pk_1
    Yk_1hat = Yk_hat

    return Yk_hat[0,0], Yk_hat[1,0]


# 初始化跟踪变量
green_threshold = (28, 54, -42, -10, 0, 30)

cx_1 = 0
cy_1 = 0
rect_w = rect_h = 40  # 默认矩形尺寸
X_est = 320  # 初始位置设置为中心
Y_est = 240
V_est = 0
YV_est = 0
MEA_V = 0
MEA_YV = 0
last_time = time.ticks_ms()

#__________________________________________功能函数定义_____________________________________________

#任务标志位改变函数
def Flag_transform():
    global Flag
    try:
        # 读取3个字节
        data = uart.read()
        # 如果读取到数据且长度为3
        if data is not None and len(data) == 3:
            # 检查包头和包尾
            if data[0] == 0xAA and data[2] == 0xAB:
                Flag = data[1]
                print(f"Flag更新为: {Flag}")
    except Exception as e:
        print(f"串口数据处理错误: {e}")

# 简单的激光发射函数（保持兼容性）
def fire_laser():
    """简单的激光发射函数"""
    try:
        # 发送激光发射指令
        laser_command = [0x2c, 0x13, 0x01, 0x5B]
        safe_uart_send(laser_command)
        return True
    except Exception as e:
        print(f"激光发射错误: {e}")
        return False

#存储脱机调整的阈值
def storage():
    global blue_threshold, binary_threshold
    try:
        # 使用保存的LAB阈值，如果没有则使用默认值
        if saved_lab_thresholds:
            blue_threshold = tuple(saved_lab_thresholds[-1])   # 使用最后一个保存的LAB阈值作为蓝色
        else:
            blue_threshold = blue_threshold  # 使用默认蓝色阈值

        # 使用保存的灰度阈值，如果没有则使用默认值
        if saved_gray_thresholds:
            binary_threshold = saved_gray_thresholds[-1]  # 使用最后保存的阈值
        else:
            binary_threshold = binary_threshold  # 默认阈值

        print(f"阈值已更新 - 蓝色: {blue_threshold}, 二值化: {binary_threshold}")
    except Exception as e:
        print(f"阈值存储错误: {e}")

#找到对应最大色块
def find_max_blob(blobs):
    if not blobs:
        return None
    max_size = 0
    max_blob = None
    for b in blobs:
        if b[2]*b[3] > max_size:
            max_blob = b
            max_size = b[2]*b[3]
    return max_blob

#找到最大矩形
def find_max_rect(rects):
    if not rects:
        return None
    max_size = 0
    max_rect = None
    for rect in rects:
        if rect.w()*rect.h() > max_size:
            max_rect = rect
            max_size = rect.w()*rect.h()
    return max_rect

#使用灰度阈值寻找黑色边框
def img_bin_rects(img):
    global binary_threshold
    try:
        # 转换到灰度图像进行矩形检测
        img_gray = img.to_grayscale(copy=True)

        # 应用二值化增强边缘
        img_binary = img_gray.binary([binary_threshold])
        rects = img_binary.find_rects(threshold=10000)

        if rects:
            return rects
        else:
            return None
    except Exception as e:
        print(f"矩形检测错误: {e}")
        return None

# 安全的串口发送函数
def safe_uart_send(data_list):
    """安全的串口发送函数，包含错误处理"""
    try:
        uart.write(bytes(data_list))
        return True
    except Exception as e:
        print(f"串口发送错误: {e}")
        return False

# 计算两点距离
def calculate_distance(x1, y1, x2, y2):
    """计算两点之间的距离"""
    return math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2)

# 激光笔控制函数
def fire_laser():
    """发射激光笔"""
    try:
        # 发送激光发射信号给单片机
        n = 9 & 0xFF
        send_lst = [0x2c, 0x12, n, 0x5B]
        safe_uart_send(send_lst)

        print("激光笔发射!")
        return True
    except Exception as e:
        print(f"激光发射错误: {e}")
        return False

#Flag==2 - 基础部分第二题：增强卡尔曼滤波黑框跟踪
def goto_target_point_1():
    global rect_cx, rect_cy, cx_1, cy_1, X_est, Y_est, V_est, YV_est, MEA_V, MEA_YV
    img = sensor.snapshot()

    rects = img_bin_rects(img)
    blue_blobs = img.find_blobs([blue_threshold], pixels_threshold=10, area_threshold=10)

    # 黑框跟踪状态
    black_frame_detected = False

    if not blue_blobs:
        if rects:
            max_rect = find_max_rect(rects)
            if max_rect:
                black_frame_detected = True

                # 计算矩形中心
                rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

                # 获取尺寸信息
                rect_w = max_rect.w()
                rect_h = max_rect.h()
                cx = rect_cx
                cy = rect_cy

                # 卡尔曼滤波跟踪
                if cx_1 == 0 and cy_1 == 0:
                    # 第一次检测，初始化位置
                    X_est, Y_est = cx, cy
                    V_est, YV_est = 0, 0
                    print(f"初始化卡尔曼滤波: 位置({X_est}, {Y_est})")
                else:
                    # 计算测量速度
                    MEA_V = (cx - cx_1) / T_seconds
                    MEA_YV = (cy - cy_1) / T_seconds

                    # 应用卡尔曼滤波
                    X_est, V_est = Kalman_x_filter(cx, MEA_V)
                    Y_est, YV_est = Kalman_y_filter(cy, MEA_YV)

                # 更新前一帧位置
                cx_1 = cx
                cy_1 = cy

                # 绘制检测结果
                # 1. 原始检测位置（红色）
                img.draw_circle(rect_cx, rect_cy, 3, color=(255, 0, 0), thickness=2, fill=True)

                # 2. 矩形轮廓（绿色）
                img.draw_rectangle(max_rect.rect(), color=(0, 255, 0), thickness=3)
                corners = max_rect.corners()
                for i in range(4):
                    start_idx = i
                    end_idx = (i + 1) % 4
                    img.draw_line(corners[start_idx][0], corners[start_idx][1],
                                 corners[end_idx][0], corners[end_idx][1],
                                 color=(0, 255, 0), thickness=2)

                # 3. 卡尔曼滤波预测位置（黄色十字）
                img.draw_cross(int(X_est), int(Y_est), color=(255, 255, 0), size=15, thickness=3)
                img.draw_circle(int(X_est), int(Y_est), 5, color=(255, 255, 0), thickness=2, fill=True)

                # 4. 预测轨迹线（青色）
                if abs(V_est) > 1 or abs(YV_est) > 1:  # 只在有明显运动时显示预测
                    future_x = int(X_est + V_est * 0.5)  # 预测0.5秒后的位置
                    future_y = int(Y_est + YV_est * 0.5)
                    img.draw_line(int(X_est), int(Y_est), future_x, future_y,
                                 color=(0, 255, 255), thickness=2)
                    img.draw_circle(future_x, future_y, 3, color=(0, 255, 255), thickness=2)

                # 使用卡尔曼滤波预测位置计算控制误差
                e_x_5 = 400 - int(X_est)  # 使用预测位置而不是原始检测位置
                e_y_5 = 240 - int(Y_est)
                distance = calculate_distance(X_est, Y_est, 400, 240)

                # 发送控制信号
                e_x_5_high = (e_x_5 >> 8) & 0xFF
                e_x_5_low = e_x_5 & 0xFF
                e_y_5_high = (e_y_5 >> 8) & 0xFF
                e_y_5_low = e_y_5 & 0xFF

                send_lst = [0x2c, 0x12, e_x_5_high, e_x_5_low, e_y_5_high, e_y_5_low, 0x5B]
                safe_uart_send(send_lst)

                # 显示跟踪信息
                img.draw_string_advanced(10, 10, 14, f"黑框: ({rect_cx}, {rect_cy})", color=(255, 0, 0))
                img.draw_string_advanced(10, 30, 14, f"预测: ({int(X_est)}, {int(Y_est)})", color=(255, 255, 0))
                img.draw_string_advanced(10, 50, 14, f"速度: ({V_est:.1f}, {YV_est:.1f})", color=(0, 255, 255))
                img.draw_string_advanced(10, 70, 14, f"误差: ({e_x_5}, {e_y_5})", color=(255, 255, 255))
                img.draw_string_advanced(10, 90, 14, f"距离: {distance:.1f}px", color=(255, 255, 255))

                # 检查激光发射条件（使用预测位置）
                if distance < 20:
                    fire_laser()
                    img.draw_string_advanced(10, 110, 16, "激光发射!", color=(0, 255, 0))
        else:
            # 没有检测到黑框，发送搜索指令
            search_cmd = [0x2c, 0x11, 0x01, 0x5B]  # 搜索旋转指令
            safe_uart_send(search_cmd)
            img.draw_string_advanced(10, 10, 16, "搜索黑框中...", color=(255, 255, 0))
    else:
        max_blue_blob = find_max_blob(blue_blobs)
        if max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            if rects:
                max_rect = find_max_rect(rects)
                if max_rect:
                    rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                    rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

                    e_x_6 = rect_cx - max_blue_blob.cx()
                    e_y_6 = rect_cy - max_blue_blob.cy()

                    e_x_6_high = (e_x_6 >> 8) & 0xFF
                    e_x_6_low = e_x_6 & 0xFF
                    e_y_6_high = (e_y_6 >> 8) & 0xFF
                    e_y_6_low = e_y_6 & 0xFF

                    send_lst = [0x2c, 0x12, e_x_6_high, e_x_6_low, e_y_6_high, e_y_6_low, 0x5B]
                    safe_uart_send(send_lst)

#                    img.draw_string_advanced(10, 10, 16, "双目标相对跟踪", color=(255, 255, 0))
    Display.show_image(img)

#Flag==3 - 基础部分第三题
def goto_target_point_2():
    global rect_cx, rect_cy

    img = sensor.snapshot()
    rects = img_bin_rects(img)
    blue_blobs = img.find_blobs([blue_threshold], pixels_threshold=10, area_threshold=10)

    if not blue_blobs:
        if not rects:
            # 旋转找黑色矩形框
            v = 8
            V = v & 0xFF
            send_lst = [0x2c, 0x12, V, 0x5B]
            safe_uart_send(send_lst)
            # 减少字符串绘制以提高帧率
            # img.draw_string_advanced(10, 10, 20, "搜索矩形中...", color=(255, 255, 0))
        else:
            max_rect = find_max_rect(rects)
            if max_rect:
                rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)
                # 绘制矩形中心点和轮廓
                img.draw_circle(rect_cx, rect_cy, 5, color=(255, 0, 0), thickness=2, fill=True)

                corners = max_rect.corners()
                for i in range(4):
                    start_idx = i
                    end_idx = (i + 1) % 4
                    img.draw_line(corners[start_idx][0], corners[start_idx][1],
                                 corners[end_idx][0], corners[end_idx][1],
                                 color=(0, 255, 0), thickness=2)
                # 计算误差
                e_x_5 = 400 - rect_cx
                e_y_5 = 240 - rect_cy
                distance = calculate_distance(rect_cx, rect_cy, 400, 240)
                # 发送控制信号
                e_x_5_high = (e_x_5 >> 8) & 0xFF
                e_x_5_low = e_x_5 & 0xFF
                e_y_5_high = (e_y_5 >> 8) & 0xFF
                e_y_5_low = e_y_5 & 0xFF
                send_lst = [0x2c, 0x12, e_x_5_high, e_x_5_low, e_y_5_high, e_y_5_low, 0x5B]
                safe_uart_send(send_lst)
                # 检查激光发射条件
                if distance < 20:
                    fire_laser()
    else:
        max_blue_blob = find_max_blob(blue_blobs)
        if max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            if rects:
                max_rect = find_max_rect(rects)
                if max_rect:
                    rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                    rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

                    e_x_6 = rect_cx - max_blue_blob.cx()
                    e_y_6 = rect_cy - max_blue_blob.cy()

                    e_x_6_high = (e_x_6 >> 8) & 0xFF
                    e_x_6_low = e_x_6 & 0xFF
                    e_y_6_high = (e_y_6 >> 8) & 0xFF
                    e_y_6_low = e_y_6 & 0xFF

                    send_lst = [0x2c, 0x12, e_x_6_high, e_x_6_low, e_y_6_high, e_y_6_low, 0x5B]
                    safe_uart_send(send_lst)

    Display.show_image(img)

#Flag==4,5 - 发挥部分第一、二题
def goto_target_point_3():
    global rect_cx, rect_cy
    img = sensor.snapshot()
    rects = img_bin_rects(img)
    blue_blobs = img.find_blobs([blue_threshold], pixels_threshold=10, area_threshold=10)

    if rects and blue_blobs:
        max_rect = find_max_rect(rects)
        max_blue_blob = find_max_blob(blue_blobs)

        if max_rect and max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
            rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

            # 绘制矩形轮廓
            corners = max_rect.corners()
            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(0, 255, 0), thickness=2)

            img.draw_circle(rect_cx, rect_cy, 5, color=(255, 0, 0), thickness=2, fill=True)

            e_x_7 = rect_cx - max_blue_blob.cx()
            e_y_7 = rect_cy - max_blue_blob.cy()

            e_x_7_high = (e_x_7 >> 8) & 0xFF
            e_x_7_low = e_x_7 & 0xFF
            e_y_7_high = (e_y_7 >> 8) & 0xFF
            e_y_7_low = e_y_7 & 0xFF

            send_lst = [0x2c, 0x12, e_x_7_high, e_x_7_low, e_y_7_high, e_y_7_low, 0x5B]
            safe_uart_send(send_lst)

            # 显示详细信息
            distance = calculate_distance(rect_cx, rect_cy, max_blue_blob.cx(), max_blue_blob.cy())

    Display.show_image(img)

#Flag==6 - 发挥部分第三题：圆环检测与跟踪
def goto_target_point_4():
    """发挥部分第三题 - 圆环检测与跟踪画圆"""
    global rect_cx, rect_cy, cx_1, cy_1, X_est, Y_est, V_est, YV_est, MEA_V, MEA_YV

    img = sensor.snapshot()

    # 圆环检测参数
    ring_params = {
        'min_outer': 25,        # 外圆最小半径
        'max_outer': 80,        # 外圆最大半径
        'min_inner': 15,        # 内圆最小半径
        'max_inner': 50,        # 内圆最大半径
        'center_tolerance': 0.3, # 圆心距离容差
        'radius_ratio_min': 0.3, # 最小半径比例
        'radius_ratio_max': 0.8  # 最大半径比例
    }

    # 检测红色区域
    red_blobs = img.find_blobs([red_threshold], pixels_threshold=20, area_threshold=100)

    if red_blobs:
        # 分离外圆和内圆候选
        outer_candidates = []
        inner_candidates = []

        for blob in red_blobs:
            area = blob.pixels()
            radius = math.sqrt(area / math.pi)

            # 计算圆形度
            perimeter = blob.perimeter()
            circularity = (4 * math.pi * area) / (perimeter * perimeter) if perimeter > 0 else 0

            # 长宽比检查
            aspect_ratio = blob.w() / blob.h() if blob.h() > 0 else 0

            if circularity > 0.3 and 0.6 <= aspect_ratio <= 1.4:
                circle_info = {
                    'blob': blob,
                    'center': (blob.cx(), blob.cy()),
                    'radius': radius,
                    'area': area,
                    'circularity': circularity
                }

                # 根据半径分类
                if ring_params['min_outer'] <= radius <= ring_params['max_outer']:
                    outer_candidates.append(circle_info)
                elif ring_params['min_inner'] <= radius <= ring_params['max_inner']:
                    inner_candidates.append(circle_info)

        # 圆环匹配
        rings = []

        if outer_candidates and inner_candidates:
            for outer in outer_candidates:
                ox, oy = outer['center']
                oradius = outer['radius']

                for inner in inner_candidates:
                    ix, iy = inner['center']
                    iradius = inner['radius']

                    # 计算圆心距离
                    center_dist = calculate_distance(ox, oy, ix, iy)

                    # 计算半径比例
                    radius_ratio = iradius / oradius if oradius > 0 else 0

                    # 圆环匹配条件
                    max_center_dist = min(oradius, iradius) * ring_params['center_tolerance']

                    if (center_dist < max_center_dist and
                        ring_params['radius_ratio_min'] < radius_ratio < ring_params['radius_ratio_max']):

                        # 计算圆环质量评分
                        center_score = 1.0 - (center_dist / max_center_dist)
                        ratio_score = 1.0 - abs(radius_ratio - 0.55) / 0.25  # 理想比例0.55
                        circularity_score = (outer['circularity'] + inner['circularity']) / 2

                        quality_score = center_score * ratio_score * circularity_score

                        rings.append({
                            'outer': outer,
                            'inner': inner,
                            'center_dist': center_dist,
                            'radius_ratio': radius_ratio,
                            'quality_score': quality_score
                        })

        # 按质量评分排序，选择最好的圆环
        rings.sort(key=lambda x: x['quality_score'], reverse=True)

        if rings:
            best_ring = rings[0]
            outer = best_ring['outer']
            inner = best_ring['inner']

            # 使用外圆中心作为跟踪目标
            cx = outer['center'][0]
            cy = outer['center'][1]

            # 卡尔曼滤波跟踪
            if cx_1 == 0 and cy_1 == 0:
                # 第一次检测，初始化位置
                X_est, Y_est = cx, cy
            else:
                # 计算速度
                MEA_V = (cx - cx_1) / T_seconds
                MEA_YV = (cy - cy_1) / T_seconds

                # 应用卡尔曼滤波
                X_est, V_est = Kalman_x_filter(cx, MEA_V)
                Y_est, YV_est = Kalman_y_filter(cy, MEA_YV)

            # 更新前一帧位置
            cx_1 = cx
            cy_1 = cy

            # 绘制圆环
            ox, oy = outer['center']
            oradius = int(outer['radius'])
            ix, iy = inner['center']
            iradius = int(inner['radius'])

            # 绘制外圆（红色）
            img.draw_circle(ox, oy, oradius, color=(255, 0, 0), thickness=3)
            # 绘制内圆（绿色）
            img.draw_circle(ix, iy, iradius, color=(0, 255, 0), thickness=2)
            # 绘制圆心连线
            img.draw_line(ox, oy, ix, iy, color=(255, 255, 0), thickness=1)

            # 绘制卡尔曼滤波预测位置
            img.draw_cross(int(X_est), int(Y_est), color=(0, 255, 255), size=15, thickness=3)
            img.draw_circle(int(X_est), int(Y_est), 5, color=(0, 255, 255), thickness=2, fill=True)

            # 计算跟踪误差并发送
            track_error_x = 400 - int(X_est)  # 使用卡尔曼滤波预测位置
            track_error_y = 240 - int(Y_est)

            # 发送跟踪控制指令
            e_x_high = (track_error_x >> 8) & 0xFF
            e_x_low = track_error_x & 0xFF
            e_y_high = (track_error_y >> 8) & 0xFF
            e_y_low = track_error_y & 0xFF

            send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
            safe_uart_send(send_lst)

            # 显示信息
            img.draw_string_advanced(10, 10, 16, f"圆环检测成功!", color=(0, 255, 0))
            img.draw_string_advanced(10, 30, 14, f"外圆: ({ox}, {oy}) R:{oradius}", color=(255, 0, 0))
            img.draw_string_advanced(10, 50, 14, f"内圆: ({ix}, {iy}) R:{iradius}", color=(0, 255, 0))
            img.draw_string_advanced(10, 70, 14, f"预测: ({int(X_est)}, {int(Y_est)})", color=(0, 255, 255))
            img.draw_string_advanced(10, 90, 14, f"质量: {best_ring['quality_score']:.3f}", color=(255, 255, 0))
            img.draw_string_advanced(10, 110, 14, f"误差: ({track_error_x}, {track_error_y})", color=(255, 255, 255))

            # 绘制跟踪线
            img.draw_line(400, 240, int(X_est), int(Y_est), color=(0, 0, 255), thickness=2)

        else:
            img.draw_string_advanced(10, 10, 16, "未找到匹配的圆环", color=(255, 255, 0))
    else:
        img.draw_string_advanced(10, 10, 16, "搜索红色圆环中...", color=(255, 255, 0))

    # 绘制屏幕中心十字线
    img.draw_cross(400, 240, color=(0, 0, 255), size=20, thickness=2)

    Display.show_image(img)

#Flag==7 - 系统监控和调试
def goto_target_point_5():
    """系统监控和调试功能"""
    img = sensor.snapshot()

    # 显示系统状态
    img.draw_string_advanced(10, 10, 20, "系统监控模式", color=(255, 255, 255))
    img.draw_string_advanced(10, 40, 16, f"当前Flag: {Flag}", color=(0, 255, 0))
    img.draw_string_advanced(10, 60, 16, f"卡尔曼位置: ({int(X_est)}, {int(Y_est)})", color=(0, 255, 255))
    img.draw_string_advanced(10, 80, 16, f"卡尔曼速度: ({V_est:.2f}, {YV_est:.2f})", color=(0, 255, 255))
    img.draw_string_advanced(10, 100, 16, f"时间间隔: {T_seconds:.3f}s", color=(255, 255, 0))

    # 显示阈值信息
    img.draw_string_advanced(10, 130, 14, f"蓝色阈值: {blue_threshold}", color=(0, 0, 255))
    img.draw_string_advanced(10, 150, 14, f"红色阈值: {red_threshold}", color=(255, 0, 0))
    img.draw_string_advanced(10, 170, 14, f"二值化阈值: {binary_threshold}", color=(255, 255, 255))

    # 显示内存和性能信息
    fps = clock.fps()
    img.draw_string_advanced(10, 200, 14, f"FPS: {fps:.1f}", color=(255, 255, 255))
    img.draw_string_advanced(10, 220, 14, f"内存清理间隔: 5s", color=(255, 255, 255))

    Display.show_image(img)

#_________________________________________脱机调阈值系统函数_____________________________________________

# 阈值验证函数
def validate_threshold():
    """验证阈值的逻辑正确性，确保min值不大于max值"""
    if current_mode == 'lab':
        # 检查L_min <= L_max
        if lab_threshold[0] > lab_threshold[1]:
            lab_threshold[0] = lab_threshold[1]
        # 检查A_min <= A_max
        if lab_threshold[2] > lab_threshold[3]:
            lab_threshold[2] = lab_threshold[3]
        # 检查B_min <= B_max
        if lab_threshold[4] > lab_threshold[5]:
            lab_threshold[4] = lab_threshold[5]
    else:  # gray mode
        # 检查gray_min <= gray_max
        if gray_threshold[0] > gray_threshold[1]:
            gray_threshold[0] = gray_threshold[1]

# 阈值调整系统主函数
def threshold_adjustment_system():
    """
    脱机调阈值系统主函数 - 自动使用HD分辨率
    返回处理后的图像，由主程序负责显示
    """
    global current_mode, threshold_adjustment_mode, Flag, binary_threshold

    # 捕获摄像头图像
    img_cam = sensor.snapshot()
    img_cam = img_cam.copy(roi=cut_roi)

    # 创建用于绘制按钮的画布
    img = sensor.snapshot()  # 使用完整的摄像头图像作为背景

    # 应用当前阈值到裁剪的图像
    if current_mode == 'gray':
        # 灰度图识别 - 二值化显示
        img_processed = img_cam.to_grayscale()
        img_processed = img_processed.binary([gray_threshold[:2]])
        img_processed = img_processed.to_rgb565()
        # 同时更新二值化阈值
        binary_threshold = gray_threshold[:2]
    else:  # 'lab'
        # LAB空间识别 - 二值化显示
        try:
            img_processed = img_cam.binary([lab_threshold])
            img_processed = img_processed.to_rgb565()
        except:
            img_processed = img_cam

    # 将处理后的图像绘制到中央
    center_x = (800 - img_processed.width()) // 2
    center_y = (480 - img_processed.height()) // 2
    img.draw_image(img_processed, center_x, center_y)

    # 绘制界面按钮
    button_color = (150, 150, 150)
    text_color = (0, 0, 0)

    # 返回按钮
    img.draw_rectangle(0, 0, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(50, 10, 20, "返回", color=text_color)

    # 切换按钮
    img.draw_rectangle(800-160, 0, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(800-160+50, 10, 20, "切换", color=text_color)

    # 归位按钮
    img.draw_rectangle(0, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(50, 480-30, 20, "归位", color=text_color)

    # 保存按钮
    img.draw_rectangle(800-160, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(800-160+50, 480-30, 20, "保存", color=text_color)

    # 左侧滑块按钮（减少值）
    button_labels_left = ["L-", "L-", "A-", "A-", "B-", "B-"] if current_mode == 'lab' else ["G-", "G-", "", "", "", ""]
    for i in range(6):
        y_pos = 60 + i * 60
        img.draw_rectangle(0, y_pos, 160, 40, color=button_color, thickness=2, fill=True)
        if button_labels_left[i]:  # 只显示有效按钮的标签
            img.draw_string_advanced(70, y_pos + 10, 20, button_labels_left[i], color=text_color)

    # 右侧滑块按钮（增加值）
    button_labels_right = ["L+", "L+", "A+", "A+", "B+", "B+"] if current_mode == 'lab' else ["G+", "G+", "", "", "", ""]
    for i in range(6):
        y_pos = 60 + i * 60
        img.draw_rectangle(800-160, y_pos, 160, 40, color=button_color, thickness=2, fill=True)
        if button_labels_right[i]:  # 只显示有效按钮的标签
            img.draw_string_advanced(800-160+70, y_pos + 10, 20, button_labels_right[i], color=text_color)

    # 显示当前模式提示
    img.draw_rectangle(300, 10, 200, 30, color=(200, 200, 200), thickness=2, fill=True)
    mode_text = "灰度模式" if current_mode == 'gray' else "LAB模式"
    img.draw_string_advanced(320, 15, 20, f"模式: {mode_text}", color=text_color)

    # 显示当前阈值值
    if current_mode == 'gray':
        img.draw_string_advanced(10, 420, 18,
                                 f"灰度阈值: [{gray_threshold[0]}, {gray_threshold[1]}]",
                                 color=text_color)
    else:
        img.draw_string_advanced(10, 420, 16,
                                 f"LAB阈值: L[{lab_threshold[0]},{lab_threshold[1]}] A[{lab_threshold[2]},{lab_threshold[3]}] B[{lab_threshold[4]},{lab_threshold[5]}]",
                                 color=text_color)

    # 处理触摸输入
    points = tp.read()
    if points:
        x, y = points[0].x, points[0].y

        # 判断按下的按钮
        def which_key(x, y):
            if x < 160:
                if y < 40: return "return"
                if y > 480-40: return "reset"
                if 60 <= y < 420: return str((y-60)//60)
            elif x > 800-160:
                if y < 40: return "change"
                if y > 480-40: return "save"
                if 60 <= y < 420: return str((y-60)//60+6)
            return None

        btn = which_key(x, y)
        if btn:
            # 返回按钮
            if btn == "return":
                threshold_adjustment_mode = False
                Flag = 0
                print("退出阈值调节模式，Flag重置为0")
                return img

            # 切换阈值模式
            elif btn == "change":
                if current_mode == 'lab':
                    current_mode = 'gray'
                    print("切换到灰度模式")
                else:
                    current_mode = 'lab'
                    print("切换到LAB模式")

            # 阈值归位
            elif btn == "reset":
                if current_mode == 'gray':
                    gray_threshold[0] = 0
                    gray_threshold[1] = 255
                    binary_threshold = gray_threshold[:2]
                    print("已重置灰度阈值为全白色背景")
                else:  # lab
                    lab_threshold[0] = 0
                    lab_threshold[1] = 100
                    lab_threshold[2] = -128
                    lab_threshold[3] = 127
                    lab_threshold[4] = -128
                    lab_threshold[5] = 127
                    print("已重置LAB阈值为全白色背景")

            # 保存当前阈值
            elif btn == "save":
                if current_mode == 'lab':
                    # LAB模式保存时需要调整A、B通道的值（减去128）
                    adj_vals = [lab_threshold[i] for i in range(6)]
                    saved_lab_thresholds.append(adj_vals.copy())
                    print(f"已保存LAB阈值: {adj_vals}")
                elif current_mode == 'gray':
                    saved_gray_thresholds.append(gray_threshold[:2].copy())
                    binary_threshold = gray_threshold[:2]
                    print(f"已保存灰度阈值: {gray_threshold[:2]}")

            # 调整滑块
            else:
                idx = int(btn)
                max_params = 2 if current_mode == 'gray' else 6

                if idx >= 6:  # 右侧按钮增加阈值
                    chan_idx = idx - 6
                    if chan_idx < max_params:
                        if current_mode == 'gray':
                            gray_threshold[chan_idx] = gray_threshold[chan_idx] + 2 # 修复：使用min而不是max
                            binary_threshold = gray_threshold[:2]
                        else:  # lab
                            if chan_idx < 2:  # L通道
                                lab_threshold[chan_idx] = lab_threshold[chan_idx] + 2
                            else:  # A, B通道
                                lab_threshold[chan_idx] = lab_threshold[chan_idx] + 2
                else:  # 左侧按钮减小阈值
                    chan_idx = idx
                    if chan_idx < max_params:
                        if current_mode == 'gray':
                            gray_threshold[chan_idx] = gray_threshold[chan_idx] - 2
                            binary_threshold = gray_threshold[:2]
                        else:  # lab
                            if chan_idx < 2:  # L通道
                                lab_threshold[chan_idx] = lab_threshold[chan_idx] - 2
                            else:  # A, B通道
                                lab_threshold[chan_idx] = lab_threshold[chan_idx] - 2

                # 验证阈值逻辑
                validate_threshold()

            time.sleep_ms(100)  # 防止重复触发

    return img

#_____________________________________________主程序________________________________________________
while True:
    try:
        clock.tick()
        os.exitpoint()

        # 串口数据处理
        if uart.any():
            Flag_transform()

        # 定期内存清理，提高性能
        if ticks_ms() % 5000 < 50:  # 每5秒清理一次内存
            gc.collect()

        # 计算时间差
        current_time = time.ticks_ms()
        T_seconds = time.ticks_diff(current_time, last_time) / 1000.0
        T_seconds = max(T_seconds, 0.01)  # 最小时间差为10ms
        last_time = current_time

        A = Matrix([[1, T_seconds], [0, 1]])

        # 性能优化：减少跳帧以提高检测精度
        frame_skip_count += 1
        if frame_skip_count <= max_frame_skip and Flag not in [1, 7]:
            continue
        frame_skip_count = 0

        # 性能优化：减少不必要的内存分配
        current_time = ticks_ms()
        if current_time - last_detection_time < detection_interval and Flag not in [1, 6, 7]:
            # 对于非关键任务，降低检测频率以提高帧率
            img = sensor.snapshot()
            Display.show_image(img)
            continue
        last_detection_time = current_time

        # 实现一个长按屏幕进入阈值编辑模式的效果（仅在非绘画模式下检测）
        if Flag != 6:  # 绘画模式下不检测触摸，避免干扰
            points = tp.read()
            if len(points) > 0:
                touch_counter += 1
                if touch_counter > 10:  # 减少触摸检测时间，提高响应速度
                    if not threshold_adjustment_mode:
                        threshold_adjustment_mode = True
                        Flag = 1
                        # 重置阈值为全白色背景的初始值
                        lab_threshold = [0, 100, -128, 127, -128, 127]  # LAB全范围，显示纯白色
                        gray_threshold = [0, 255]  # 灰度全范围，显示纯白色
                        current_mode = 'lab'  # 默认进入LAB模式
                        print("进入阈值调节模式 - 初始化为纯白色背景")
                        touch_counter = 0  # 重置计数器
                # 减少调试输出，提高性能
                # print(f"触摸位置: {points[0].x}, {points[0].y}")
            else:
                touch_counter -= 2
                touch_counter = max(0, touch_counter)

        # 主要功能逻辑
        if Flag == 0:
            img = sensor.snapshot()
            Display.show_image(img) #显示图片

        elif Flag == 1:
            # 检查是否进入阈值调节模式
            if threshold_adjustment_mode:
                # 执行阈值调节系统（自动切换到HD分辨率）
                img = threshold_adjustment_system()
                storage()
                Display.show_image(img)

        #E题基本要求(2)：2秒内瞄准靶心，D1≤2cm
        elif Flag == 2:
            goto_target_point_1()

        #E题基本要求(3)：4秒内自动瞄准，D1≤2cm
        elif Flag == 3:
            goto_target_point_2()

        #E题发挥(1)：N=1圈，t≤20s，D1≤2cm
        elif Flag == 4:
            goto_target_point_3()

        #E题发挥(2)：N=2圈，t≤40s，D1≤2cm
        elif Flag == 5:
            goto_target_point_3()

        #发挥部分第三题：圆环检测与跟踪
        elif Flag == 6:
            goto_target_point_4()

        #系统监控和调试
        elif Flag == 7:
            goto_target_point_5()

    except Exception as e:
        print(f"主程序错误: {e}")
        # 错误恢复：重置到安全状态
        Flag = 0
        threshold_adjustment_mode = False
        time.sleep_ms(100)

