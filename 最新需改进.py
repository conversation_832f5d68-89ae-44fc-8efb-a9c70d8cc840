import time
import os
import math
import gc

from time import ticks_ms
from machine import FPIOA
from machine import UART
from machine import TOUCH

from media.sensor import *
from media.display import *
from media.media import *

#______________________________________变量定义并赋初值___________________________________________

#任务标志位，通过串口接受数据改变
Flag = 2

# Flag = 0   正常拍照
# Flag = 1   脱机调整阈值并且存储
# Flag = 2   基础部分第二题
# Flag = 3   基础部分第三题
# Flag = 4   发挥部分第一题
# Flag = 5   发挥部分第二题
# Flag = 6   发挥部分第三题
# Flag = 7   其他部分

# 新增：性能优化变量
max_frame_skip = 0  # 不跳帧，提高帧率
frame_skip_count = 0  # 跳帧计数

# 性能优化：减少不必要的计算
last_detection_time = 0
detection_interval = 50  # 检测间隔(ms)

# 脱机调阈值系统的全局变量
threshold_adjustment_mode = False  # 是否进入阈值调节模式
current_mode = 'lab'  # 当前工作模式：'lab' 或 'gray'
lab_threshold = [0, 100, -128, 127, -128, 127]  # LAB阈值
gray_threshold = [0, 255]  # 灰度阈值
saved_lab_thresholds = []  # 保存的LAB阈值列表
saved_gray_thresholds = []  # 保存的灰度阈值列表

#颜色阈值
blue_threshold = (30, 50, -50, -15, -10, 30)
red_threshold = (30, 100, 15, 127, 15, 127)  # 红色圆检测阈值
binary_threshold = (45, 255)

#矩形中心的坐标
rect_cx = None
rect_cy = None

# 屏幕中心通过get_screen_center()函数获取

#触摸次数
touch_counter = 0

# 图像裁剪ROI（用于阈值调节） - 400x240分辨率适配
cut_roi = (100, 60, 200, 120)  # 中心区域裁剪

#_________________________________________模块初始化_____________________________________________

#摄像头传感器初始化 - 固定使用400x240分辨率
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width = 400, height = 240)  # 固定400x240分辨率
sensor.set_pixformat(Sensor.RGB565)

# 分辨率管理变量 - 固定400x240分辨率
current_width = 400
current_height = 240

#串口初始化
fpioa = FPIOA()
# UART1代码
fpioa.set_function(3,FPIOA.UART1_TXD)
fpioa.set_function(4,FPIOA.UART1_RXD)

uart=UART(UART.UART1,115200) #设置串口号1和波特率

#fpioa.set_function(52,FPIOA.GPIO52)
#LED=Pin(52,Pin.OUT) #构建led对象，GPIO52,输出

#缓冲区和3.5寸的屏幕图像显示
Display.init(Display.ST7701, to_ide=True)

# 初始化媒体管理器
MediaManager.init()

#启动摄像头传感器模块
sensor.run()

#时钟模块建立
clock = time.clock()

# 时间管理相关变量已删除

#建立触摸屏
tp = TOUCH(0)

#__________________________________________功能函数定义_____________________________________________

#任务标志位改变函数
def Flag_transform():
    global Flag
    try:
        # 读取3个字节
        data = uart.read()
        # 如果读取到数据且长度为3
        if data is not None and len(data) == 3:
            # 检查包头和包尾
            if data[0] == 0xAA and data[2] == 0xAB:
                Flag = data[1]
                print(f"Flag更新为: {Flag}")
    except Exception as e:
        print(f"串口数据处理错误: {e}")

# 简单的激光发射函数（保持兼容性）
def fire_laser():
    """简单的激光发射函数"""
    try:
        # 发送激光发射指令
        laser_command = [0x2c, 0x13, 0x01, 0x5B]
        safe_uart_send(laser_command)
        return True
    except Exception as e:
        print(f"激光发射错误: {e}")
        return False

#存储脱机调整的阈值
def storage():
    global blue_threshold, binary_threshold
    try:
        # 使用保存的LAB阈值，如果没有则使用默认值
        if saved_lab_thresholds:
            blue_threshold = tuple(saved_lab_thresholds[-1])   # 使用最后一个保存的LAB阈值作为蓝色
        else:
            blue_threshold = blue_threshold  # 使用默认蓝色阈值

        # 使用保存的灰度阈值，如果没有则使用默认值
        if saved_gray_thresholds:
            binary_threshold = saved_gray_thresholds[-1]  # 使用最后保存的阈值
        else:
            binary_threshold = binary_threshold  # 默认阈值

        print(f"阈值已更新 - 蓝色: {blue_threshold}, 二值化: {binary_threshold}")
    except Exception as e:
        print(f"阈值存储错误: {e}")

#找到对应最大色块
def find_max_blob(blobs):
    if not blobs:
        return None
    max_size = 0
    max_blob = None
    for b in blobs:
        if b[2]*b[3] > max_size:
            max_blob = b
            max_size = b[2]*b[3]
    return max_blob

#找到最大矩形
def find_max_rect(rects):
    if not rects:
        return None
    max_size = 0
    max_rect = None
    for rect in rects:
        if rect.w()*rect.h() > max_size:
            max_rect = rect
            max_size = rect.w()*rect.h()
    return max_rect

#使用灰度阈值寻找黑色边框
def img_bin_rects(img):
    global binary_threshold
    try:
        # 转换到灰度图像进行矩形检测
        img_gray = img.to_grayscale(copy=True)

        # 应用二值化增强边缘
        img_binary = img_gray.binary([binary_threshold])
        rects = img_binary.find_rects(threshold=10000)

        if rects:
            return rects
        else:
            return None
    except Exception as e:
        print(f"矩形检测错误: {e}")
        return None

# 安全的串口发送函数
def safe_uart_send(data_list):
    """安全的串口发送函数，包含错误处理"""
    try:
        uart.write(bytes(data_list))
        return True
    except Exception as e:
        print(f"串口发送错误: {e}")
        return False

# 计算两点距离
def calculate_distance(x1, y1, x2, y2):
    """计算两点之间的距离"""
    return math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2)

#_________________________________________分辨率管理函数_____________________________________________

# 分辨率切换函数已删除 - 固定使用400x240分辨率

def show_image_400x240(img):
    """400x240分辨率专用显示函数"""
    try:
        # 在图像上添加分辨率标识（右上角，小字体）
        img.draw_string_advanced(320, 5, 10, f"400x240", color=(255, 255, 0))

        # 直接显示图像
        Display.show_image(img)

    except Exception as e:
        print(f"显示错误: {e}")
        # 如果出错，尝试不添加标识直接显示
        try:
            Display.show_image(img)
        except Exception as e2:
            print(f"显示完全失败: {e2}")

# 为了兼容性，保留原函数名
def adaptive_display_image(img):
    """兼容性包装函数 - 400x240分辨率"""
    show_image_400x240(img)

def get_screen_center():
    """获取400x240分辨率下的屏幕中心坐标"""
    return 200, 120  # 400x240分辨率中心

def scale_coordinates_to_control_system(x, y):
    """将400x240坐标转换为控制系统坐标（转换为HD坐标系作为控制基准）"""
    # 400x240 → 800x480 的缩放比例
    scale_x = 800 / 400  # 2.0倍
    scale_y = 480 / 240  # 2.0倍
    return int(x * scale_x), int(y * scale_y)

def calculate_control_error(target_x, target_y):
    """计算控制误差（转换为HD坐标系）"""
    # 获取屏幕中心
    screen_center_x, screen_center_y = get_screen_center()

    # 计算400x240分辨率下的误差
    error_x = screen_center_x - target_x
    error_y = screen_center_y - target_y

    # 转换为控制系统坐标（HD坐标系）
    control_error_x, control_error_y = scale_coordinates_to_control_system(error_x, error_y)

    return control_error_x, control_error_y

#_________________________________________几何圆环计算函数_____________________________________________

# A4纸和题目几何参数 - 适配400x240分辨率
A4_GEOMETRY = {
    'width_mm': 210,        # A4纸宽度(mm)
    'height_mm': 297,       # A4纸长度(mm)
    'tape_width_mm': 18,    # 黑色胶带宽度1.8cm
    'circle_radii_mm': [20, 40, 60, 80, 100],  # 红色圆半径2,4,6,8,10cm
    'effective_width_mm': 210 - 2*18,   # 有效宽度174mm
    'effective_height_mm': 297 - 2*18,  # 有效高度261mm

    # 400x240分辨率相关参数
    'display_width': 400,
    'display_height': 240,
    'aspect_ratio': 400/240,  # 1.67:1 宽高比
}

def calculate_pixel_to_mm_ratio(black_frame_width, black_frame_height):
    """基于检测到的黑框尺寸计算像素到毫米的转换比例"""
    try:
        # 计算X和Y方向的像素比例
        ratio_x = A4_GEOMETRY['effective_width_mm'] / black_frame_width
        ratio_y = A4_GEOMETRY['effective_height_mm'] / black_frame_height

        # 取平均值作为最终比例
        pixel_to_mm_ratio = (ratio_x + ratio_y) / 2

        return pixel_to_mm_ratio, ratio_x, ratio_y

    except Exception as e:
        print(f"像素比例计算错误: {e}")
        return 1.0, 1.0, 1.0

def calculate_circle_positions(center_x, center_y, pixel_to_mm_ratio):
    """基于几何关系计算所有红色圆的位置"""
    try:
        circles = []

        for i, radius_mm in enumerate(A4_GEOMETRY['circle_radii_mm']):
            # 转换为像素半径
            radius_px = int(radius_mm / pixel_to_mm_ratio)

            circle_info = {
                'index': i + 1,                    # 第几个圆(1-5)
                'radius_mm': radius_mm,            # 毫米半径
                'radius_px': radius_px,            # 像素半径
                'center_x': center_x,              # 圆心X坐标
                'center_y': center_y,              # 圆心Y坐标
                'is_target': (i == 2)              # 是否是第三个圆(目标圆)
            }

            circles.append(circle_info)

        return circles

    except Exception as e:
        print(f"圆环位置计算错误: {e}")
        return []

def draw_calculated_circles(img, circles, show_all=True):
    """绘制计算出的圆环位置"""
    try:
        for circle in circles:
            center_x = circle['center_x']
            center_y = circle['center_y']
            radius = circle['radius_px']
            index = circle['index']
            is_target = circle['is_target']

            # 检查圆是否在图像范围内
            if (center_x - radius < 0 or center_x + radius >= current_width or
                center_y - radius < 0 or center_y + radius >= current_height):
                continue

            if show_all:
                if is_target:
                    # 第三个圆（目标圆）用红色粗线
                    color = (255, 0, 0)
                    thickness = 3
                    # 绘制虚线效果
                    for angle in range(0, 360, 15):
                        angle_rad = math.radians(angle)
                        x1 = int(center_x + radius * math.cos(angle_rad))
                        y1 = int(center_y + radius * math.sin(angle_rad))
                        angle_rad2 = math.radians(angle + 8)
                        x2 = int(center_x + radius * math.cos(angle_rad2))
                        y2 = int(center_y + radius * math.sin(angle_rad2))
                        img.draw_line(x1, y1, x2, y2, color=color, thickness=thickness)

                    # 标记第三个圆
                    img.draw_string_advanced(center_x - 30, center_y - radius - 25, 14,
                                           f"第3圆(目标)", color=color)
                else:
                    # 其他圆用较细的线
                    color = (0, 255, 255)  # 青色
                    thickness = 1
                    img.draw_circle(center_x, center_y, radius, color=color, thickness=thickness)

                    # 标记圆的序号
                    img.draw_string_advanced(center_x - 10, center_y - radius - 15, 10,
                                           f"{index}", color=color)
            else:
                # 只显示第三个圆
                if is_target:
                    color = (255, 0, 0)
                    thickness = 3
                    img.draw_circle(center_x, center_y, radius, color=color, thickness=thickness)
                    img.draw_cross(center_x, center_y, color=color, size=15, thickness=thickness)

        return True

    except Exception as e:
        print(f"圆环绘制错误: {e}")
        return False

def get_third_circle_info(circles):
    """获取第三个圆的信息"""
    try:
        for circle in circles:
            if circle['is_target']:
                return circle
        return None
    except:
        return None

#_________________________________________任务函数_____________________________________________

#Flag==6 - 发挥部分第三题：基于几何关系的圆环检测
def goto_target_point_4():
    """发挥部分第三题 - 基于黑框几何关系检测第三个红色圆"""
    global rect_cx, rect_cy

    # 使用400x240分辨率进行实时检测

    img = sensor.snapshot()

    # 检测黑框
    rects = img_bin_rects(img)

    if rects:
        max_rect = find_max_rect(rects)
        if max_rect:
            # 计算黑框中心
            corners = max_rect.corners()
            center_x = int(sum(p[0] for p in corners) / 4)
            center_y = int(sum(p[1] for p in corners) / 4)

            # 绘制黑框
            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(0, 255, 0), thickness=2)

            img.draw_circle(center_x, center_y, 3, color=(0, 255, 0), thickness=2, fill=True)

            # 基于几何关系计算圆环位置
            pixel_ratio, _, _ = calculate_pixel_to_mm_ratio(max_rect.w(), max_rect.h())
            circles = calculate_circle_positions(center_x, center_y, pixel_ratio)

            if circles:
                # 绘制所有计算出的圆环
                draw_calculated_circles(img, circles, show_all=True)

                # 获取第三个圆信息
                third_circle = get_third_circle_info(circles)

                if third_circle:
                    # 直接使用第三个圆的几何中心
                    target_x = third_circle['center_x']
                    target_y = third_circle['center_y']

                    # 绘制目标位置
                    img.draw_cross(target_x, target_y, color=(255, 255, 0), size=15, thickness=3)

                    # 计算控制误差（自动处理坐标转换）
                    control_error_x, control_error_y = calculate_control_error(target_x, target_y)

                    # 发送跟踪控制指令
                    e_x_high = (control_error_x >> 8) & 0xFF
                    e_x_low = control_error_x & 0xFF
                    e_y_high = (control_error_y >> 8) & 0xFF
                    e_y_low = control_error_y & 0xFF

                    send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
                    safe_uart_send(send_lst)

                    # 显示信息
                    screen_center_x, screen_center_y = get_screen_center()
                    img.draw_string_advanced(10, 10, 16, f"第三个圆检测成功!", color=(255, 0, 0))
                    img.draw_string_advanced(10, 30, 14, f"几何中心: ({center_x}, {center_y})", color=(0, 255, 0))
                    img.draw_string_advanced(10, 50, 14, f"目标位置: ({target_x}, {target_y})", color=(255, 255, 0))
                    img.draw_string_advanced(10, 70, 14, f"第三圆半径: {third_circle['radius_px']}px", color=(255, 0, 0))
                    img.draw_string_advanced(10, 90, 14, f"像素比例: {pixel_ratio:.2f}mm/px", color=(0, 255, 255))
                    img.draw_string_advanced(10, 110, 14, f"控制误差: ({control_error_x}, {control_error_y})", color=(255, 255, 255))
                    img.draw_string_advanced(10, 130, 14, f"屏幕中心: ({screen_center_x}, {screen_center_y})", color=(255, 255, 255))

                    # 绘制跟踪线
                    img.draw_line(screen_center_x, screen_center_y, target_x, target_y,
                                 color=(0, 0, 255), thickness=2)
                else:
                    img.draw_string_advanced(10, 10, 16, "第三个圆计算失败", color=(255, 255, 0))
            else:
                img.draw_string_advanced(10, 10, 16, "圆环位置计算失败", color=(255, 255, 0))
        else:
            img.draw_string_advanced(10, 10, 16, "未检测到有效黑框", color=(255, 255, 0))
    else:
        img.draw_string_advanced(10, 10, 16, "搜索黑框中...", color=(255, 255, 0))

    # 绘制屏幕中心十字线
    screen_center_x, screen_center_y = get_screen_center()
    img.draw_cross(screen_center_x, screen_center_y, color=(0, 0, 255), size=15, thickness=2)

    # 显示分辨率信息
    img.draw_string_advanced(10, 130, 12, f"分辨率: {current_width}x{current_height}", color=(255, 255, 255))

    # 使用自适应显示
    adaptive_display_image(img)

#Flag==7 - 系统监控和调试
def goto_target_point_5():
    """系统监控和调试功能 - 400x240分辨率适配"""
    img = sensor.snapshot()

    # 400x240分辨率的显示布局
    img.draw_string_advanced(10, 10, 16, "系统监控模式", color=(255, 255, 255))
    img.draw_string_advanced(10, 30, 12, f"Flag: {Flag}", color=(0, 255, 0))
    img.draw_string_advanced(10, 50, 12, f"分辨率: {current_width}x{current_height}", color=(255, 255, 0))
    img.draw_string_advanced(10, 70, 12, f"阈值调节模式: {'开启' if threshold_adjustment_mode else '关闭'}", color=(0, 255, 255))

    # 显示阈值信息
    img.draw_string_advanced(10, 100, 10, f"蓝色阈值: {blue_threshold}", color=(0, 0, 255))
    img.draw_string_advanced(10, 120, 10, f"红色阈值: {red_threshold}", color=(255, 0, 0))
    img.draw_string_advanced(10, 140, 10, f"二值化阈值: {binary_threshold}", color=(255, 255, 255))

    # 显示性能信息
    fps = clock.fps()
    img.draw_string_advanced(10, 170, 10, f"帧率: {fps:.1f} FPS", color=(255, 255, 255))

    # 右侧显示更多信息
    img.draw_string_advanced(220, 10, 12, "400x240模式", color=(255, 255, 0))
    img.draw_string_advanced(220, 30, 10, "固定分辨率", color=(255, 255, 0))
    img.draw_string_advanced(220, 50, 10, "高性能模式", color=(0, 255, 0))
    img.draw_string_advanced(220, 70, 10, "等比例UI", color=(0, 255, 0))

    # 显示屏幕中心信息
    screen_center_x, screen_center_y = get_screen_center()
    img.draw_string_advanced(10, 200, 10, f"屏幕中心: ({screen_center_x}, {screen_center_y})", color=(255, 255, 255))

    # 绘制屏幕中心十字线
    img.draw_cross(screen_center_x, screen_center_y, color=(0, 255, 0), size=15, thickness=2)

    # 使用自适应显示
    adaptive_display_image(img)

# 激光笔控制函数
def fire_laser():
    """发射激光笔"""
    try:
        # 发送激光发射信号给单片机
        n = 9 & 0xFF
        send_lst = [0x2c, 0x12, n, 0x5B]
        safe_uart_send(send_lst)

        print("激光笔发射!")
        return True
    except Exception as e:
        print(f"激光发射错误: {e}")
        return False

#Flag==2 - 基础部分第二题
def goto_target_point_1():
    global rect_cx, rect_cy
    img = sensor.snapshot()

    rects = img_bin_rects(img)
    blue_blobs = img.find_blobs([blue_threshold], pixels_threshold=10, area_threshold=10)

    if not blue_blobs:
        if rects:
            max_rect = find_max_rect(rects)
            if max_rect:
                rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

                # 绘制矩形中心点和轮廓
                img.draw_circle(rect_cx, rect_cy, 2, color=(255, 0, 0), thickness=2, fill=True)
                img.draw_rectangle(max_rect.rect(), color=(0, 255, 0), thickness=3)
                img.draw_cross(rect_cx, rect_cy, color=(255, 255, 0), size=15, thickness=3)

                corners = max_rect.corners()
                for i in range(4):
                    start_idx = i
                    end_idx = (i + 1) % 4
                    img.draw_line(corners[start_idx][0], corners[start_idx][1],
                                 corners[end_idx][0], corners[end_idx][1],
                                 color=(255, 0, 0), thickness=2)

                # 计算控制误差（自动处理坐标转换）
                control_error_x, control_error_y = calculate_control_error(rect_cx, rect_cy)

                # 计算距离（用于激光发射判断）
                screen_center_x, screen_center_y = get_screen_center()
                distance = calculate_distance(rect_cx, rect_cy, screen_center_x, screen_center_y)

                # 发送控制信号
                e_x_5_high = (control_error_x >> 8) & 0xFF
                e_x_5_low = control_error_x & 0xFF
                e_y_5_high = (control_error_y >> 8) & 0xFF
                e_y_5_low = control_error_y & 0xFF

                send_lst = [0x2c, 0x12, e_x_5_high, e_x_5_low, e_y_5_high, e_y_5_low, 0x5B]
                safe_uart_send(send_lst)

                # 检查激光发射条件
                if distance < 20:
                    fire_laser()
    else:
        max_blue_blob = find_max_blob(blue_blobs)
        if max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            if rects:
                max_rect = find_max_rect(rects)
                if max_rect:
                    rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                    rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

                    # 计算黑框与蓝色激光点的误差
                    local_error_x = rect_cx - max_blue_blob.cx()
                    local_error_y = rect_cy - max_blue_blob.cy()

                    # 转换为控制系统坐标
                    control_error_x, control_error_y = scale_coordinates_to_control_system(local_error_x, local_error_y)

                    e_x_6_high = (control_error_x >> 8) & 0xFF
                    e_x_6_low = control_error_x & 0xFF
                    e_y_6_high = (control_error_y >> 8) & 0xFF
                    e_y_6_low = control_error_y & 0xFF

                    send_lst = [0x2c, 0x12, e_x_6_high, e_x_6_low, e_y_6_high, e_y_6_low, 0x5B]
                    safe_uart_send(send_lst)

#                    img.draw_string_advanced(10, 10, 16, "双目标相对跟踪", color=(255, 255, 0))
    adaptive_display_image(img)

#Flag==3 - 基础部分第三题
def goto_target_point_2():
    global rect_cx, rect_cy

    img = sensor.snapshot()
    rects = img_bin_rects(img)
    blue_blobs = img.find_blobs([blue_threshold], pixels_threshold=10, area_threshold=10)

    if not blue_blobs:
        if not rects:
            # 旋转找黑色矩形框
            v = 8
            V = v & 0xFF
            send_lst = [0x2c, 0x12, V, 0x5B]
            safe_uart_send(send_lst)
            # 减少字符串绘制以提高帧率
            # img.draw_string_advanced(10, 10, 20, "搜索矩形中...", color=(255, 255, 0))
        else:
            max_rect = find_max_rect(rects)
            if max_rect:
                rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)
                # 绘制矩形中心点和轮廓
                img.draw_circle(rect_cx, rect_cy, 5, color=(255, 0, 0), thickness=2, fill=True)

                corners = max_rect.corners()
                for i in range(4):
                    start_idx = i
                    end_idx = (i + 1) % 4
                    img.draw_line(corners[start_idx][0], corners[start_idx][1],
                                 corners[end_idx][0], corners[end_idx][1],
                                 color=(0, 255, 0), thickness=2)
                # 计算控制误差（自动处理坐标转换）
                control_error_x, control_error_y = calculate_control_error(rect_cx, rect_cy)

                # 计算距离（用于激光发射判断）
                screen_center_x, screen_center_y = get_screen_center()
                distance = calculate_distance(rect_cx, rect_cy, screen_center_x, screen_center_y)

                # 发送控制信号
                e_x_5_high = (control_error_x >> 8) & 0xFF
                e_x_5_low = control_error_x & 0xFF
                e_y_5_high = (control_error_y >> 8) & 0xFF
                e_y_5_low = control_error_y & 0xFF
                send_lst = [0x2c, 0x12, e_x_5_high, e_x_5_low, e_y_5_high, e_y_5_low, 0x5B]
                safe_uart_send(send_lst)
                # 检查激光发射条件
                if distance < 20:
                    fire_laser()
    else:
        max_blue_blob = find_max_blob(blue_blobs)
        if max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            if rects:
                max_rect = find_max_rect(rects)
                if max_rect:
                    rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
                    rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

                    # 计算黑框与蓝色激光点的误差
                    local_error_x = rect_cx - max_blue_blob.cx()
                    local_error_y = rect_cy - max_blue_blob.cy()

                    # 转换为控制系统坐标
                    control_error_x, control_error_y = scale_coordinates_to_control_system(local_error_x, local_error_y)

                    e_x_6_high = (control_error_x >> 8) & 0xFF
                    e_x_6_low = control_error_x & 0xFF
                    e_y_6_high = (control_error_y >> 8) & 0xFF
                    e_y_6_low = control_error_y & 0xFF

                    send_lst = [0x2c, 0x12, e_x_6_high, e_x_6_low, e_y_6_high, e_y_6_low, 0x5B]
                    safe_uart_send(send_lst)

    adaptive_display_image(img)

#Flag==4,5 - 发挥部分第一、二题
def goto_target_point_3():
    global rect_cx, rect_cy
    img = sensor.snapshot()
    rects = img_bin_rects(img)
    blue_blobs = img.find_blobs([blue_threshold], pixels_threshold=10, area_threshold=10)

    if rects and blue_blobs:
        max_rect = find_max_rect(rects)
        max_blue_blob = find_max_blob(blue_blobs)

        if max_rect and max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
            rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

            # 绘制矩形轮廓
            corners = max_rect.corners()
            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(0, 255, 0), thickness=2)

            img.draw_circle(rect_cx, rect_cy, 5, color=(255, 0, 0), thickness=2, fill=True)

            # 计算黑框与蓝色激光点的误差
            local_error_x = rect_cx - max_blue_blob.cx()
            local_error_y = rect_cy - max_blue_blob.cy()

            # 转换为控制系统坐标
            control_error_x, control_error_y = scale_coordinates_to_control_system(local_error_x, local_error_y)

            e_x_7_high = (control_error_x >> 8) & 0xFF
            e_x_7_low = control_error_x & 0xFF
            e_y_7_high = (control_error_y >> 8) & 0xFF
            e_y_7_low = control_error_y & 0xFF

            send_lst = [0x2c, 0x12, e_x_7_high, e_x_7_low, e_y_7_high, e_y_7_low, 0x5B]
            safe_uart_send(send_lst)

            # 显示详细信息
            img.draw_string_advanced(10, 10, 16, "双目标跟踪模式", color=(255, 255, 0))

    adaptive_display_image(img)

#Flag==6 - 发挥部分第三题
#def goto_target_point_4():


#Flag==6 - 发挥部分加分项
#goto_target_point_5()

#_________________________________________脱机调阈值系统函数_____________________________________________

# 阈值验证函数
def validate_threshold():
    """验证阈值的逻辑正确性，确保min值不大于max值"""
    if current_mode == 'lab':
        # 检查L_min <= L_max
        if lab_threshold[0] > lab_threshold[1]:
            lab_threshold[0] = lab_threshold[1]
        # 检查A_min <= A_max
        if lab_threshold[2] > lab_threshold[3]:
            lab_threshold[2] = lab_threshold[3]
        # 检查B_min <= B_max
        if lab_threshold[4] > lab_threshold[5]:
            lab_threshold[4] = lab_threshold[5]
    else:  # gray mode
        # 检查gray_min <= gray_max
        if gray_threshold[0] > gray_threshold[1]:
            gray_threshold[0] = gray_threshold[1]

# 阈值调整系统主函数
def threshold_adjustment_system():
    """
    脱机调阈值系统主函数 - 400x240分辨率等比例缩放版本
    返回处理后的图像，由主程序负责显示
    """
    global current_mode, threshold_adjustment_mode, Flag, binary_threshold

    print("阈值调节模式：使用400x240分辨率")

    # 捕获摄像头图像
    img_cam = sensor.snapshot()
    img_cam = img_cam.copy(roi=cut_roi)

    # 创建用于绘制按钮的画布
    img = sensor.snapshot()  # 使用完整的摄像头图像作为背景

    # 应用当前阈值到裁剪的图像
    if current_mode == 'gray':
        # 灰度图识别 - 二值化显示
        img_processed = img_cam.to_grayscale()
        img_processed = img_processed.binary([gray_threshold[:2]])
        img_processed = img_processed.to_rgb565()
        # 同时更新二值化阈值
        binary_threshold = gray_threshold[:2]
    else:  # 'lab'
        # LAB空间识别 - 二值化显示
        try:
            img_processed = img_cam.binary([lab_threshold])
            img_processed = img_processed.to_rgb565()
        except:
            img_processed = img_cam

    # 将处理后的图像绘制到中央（400x240分辨率适配）
    center_x = (400 - img_processed.width()) // 2
    center_y = (240 - img_processed.height()) // 2
    img.draw_image(img_processed, center_x, center_y)

    # 绘制界面按钮（400x240分辨率等比例缩放）
    button_color = (150, 150, 150)
    text_color = (0, 0, 0)

    # 返回按钮（等比例缩放：160->80, 40->20）
    img.draw_rectangle(0, 0, 80, 20, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(25, 5, 10, "返回", color=text_color)

    # 切换按钮（等比例缩放：400-80=320）
    img.draw_rectangle(400-80, 0, 80, 20, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(400-80+25, 5, 10, "切换", color=text_color)

    # 归位按钮（等比例缩放：240-20=220）
    img.draw_rectangle(0, 240-20, 80, 20, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(25, 240-15, 10, "归位", color=text_color)

    # 保存按钮
    img.draw_rectangle(400-80, 240-20, 80, 20, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(400-80+25, 240-15, 10, "保存", color=text_color)

    # 左侧滑块按钮（减少值）- 等比例缩放
    button_labels_left = ["L-", "L-", "A-", "A-", "B-", "B-"] if current_mode == 'lab' else ["G-", "G-", "", "", "", ""]
    for i in range(6):
        y_pos = 30 + i * 30  # 等比例缩放：60->30, 60->30
        img.draw_rectangle(0, y_pos, 80, 20, color=button_color, thickness=2, fill=True)
        if button_labels_left[i]:  # 只显示有效按钮的标签
            img.draw_string_advanced(35, y_pos + 5, 10, button_labels_left[i], color=text_color)

    # 右侧滑块按钮（增加值）- 等比例缩放
    button_labels_right = ["L+", "L+", "A+", "A+", "B+", "B+"] if current_mode == 'lab' else ["G+", "G+", "", "", "", ""]
    for i in range(6):
        y_pos = 30 + i * 30  # 等比例缩放
        img.draw_rectangle(400-80, y_pos, 80, 20, color=button_color, thickness=2, fill=True)
        if button_labels_right[i]:  # 只显示有效按钮的标签
            img.draw_string_advanced(400-80+35, y_pos + 5, 10, button_labels_right[i], color=text_color)

    # 显示当前模式提示（等比例缩放：300->150, 200->100, 30->15）
    img.draw_rectangle(150, 5, 100, 15, color=(200, 200, 200), thickness=2, fill=True)
    mode_text = "灰度模式" if current_mode == 'gray' else "LAB模式"
    img.draw_string_advanced(160, 8, 10, f"模式: {mode_text}", color=text_color)

    # 显示当前阈值值（等比例缩放：420->210）
    if current_mode == 'gray':
        img.draw_string_advanced(5, 210, 9,
                                 f"灰度阈值: [{gray_threshold[0]}, {gray_threshold[1]}]",
                                 color=text_color)
    else:
        img.draw_string_advanced(5, 210, 8,
                                 f"LAB阈值: L[{lab_threshold[0]},{lab_threshold[1]}] A[{lab_threshold[2]},{lab_threshold[3]}] B[{lab_threshold[4]},{lab_threshold[5]}]",
                                 color=text_color)

    # 在中间图像下面显示已保存的阈值（等比例缩放）
    img_center_x = 200  # 屏幕中心X坐标（400/2）
    saved_start_y = 160  # 保存阈值显示的起始Y坐标（320/2）

    # 显示已保存阈值标题
    img.draw_string_advanced(img_center_x - 30, saved_start_y, 9, "已保存阈值:", color=text_color)

    # 显示各模式的保存阈值
    y_offset = 12  # 等比例缩放：25->12
    if saved_lab_thresholds:
        img.draw_string_advanced(img_center_x - 40, saved_start_y + y_offset, 8,
                               f"LAB模式({len(saved_lab_thresholds)}组):", color=text_color)
        y_offset += 10

        # 显示最近保存的2组阈值
        for i, threshold_val in enumerate(saved_lab_thresholds[-2:]):
            threshold_str = str(threshold_val)
            if len(threshold_str) > 17:  # 等比例缩放：35->17
                threshold_str = threshold_str[:17] + "..."
            img.draw_string_advanced(img_center_x - 50, saved_start_y + y_offset, 7,
                                   f"  {i+1}: {threshold_str}", color=text_color)
            y_offset += 9  # 等比例缩放：18->9
        y_offset += 3

    if saved_gray_thresholds:
        img.draw_string_advanced(img_center_x - 40, saved_start_y + y_offset, 8,
                               f"灰度模式({len(saved_gray_thresholds)}组):", color=text_color)
        y_offset += 10

        # 显示最近保存的2组阈值
        for i, threshold_val in enumerate(saved_gray_thresholds[-2:]):
            img.draw_string_advanced(img_center_x - 50, saved_start_y + y_offset, 7,
                                   f"  {i+1}: {threshold_val}", color=text_color)
            y_offset += 9

    # 处理触摸输入（等比例缩放的触摸区域）
    points = tp.read()
    if points:
        x, y = points[0].x, points[0].y

        # 判断按下的按钮（等比例缩放：160->80, 40->20, 480->240, 60->30）
        def which_key(x, y):
            if x < 80:  # 左侧按钮区域
                if y < 20: return "return"  # 返回按钮
                if y > 240-20: return "reset"  # 归位按钮
                if 30 <= y < 210: return str((y-30)//30)  # 滑块按钮（30开始，每30一个）
            elif x > 400-80:  # 右侧按钮区域
                if y < 20: return "change"  # 切换按钮
                if y > 240-20: return "save"  # 保存按钮
                if 30 <= y < 210: return str((y-30)//30+6)  # 滑块按钮
            return None

        btn = which_key(x, y)
        if btn:
            # 返回按钮
            if btn == "return":
                threshold_adjustment_mode = False
                Flag = 0
                print("退出阈值调节模式，Flag重置为0")
                return img

            # 切换阈值模式
            elif btn == "change":
                if current_mode == 'lab':
                    current_mode = 'gray'
                    print("切换到灰度模式")
                else:
                    current_mode = 'lab'
                    print("切换到LAB模式")

            # 阈值归位
            elif btn == "reset":
                if current_mode == 'gray':
                    gray_threshold[0] = 0
                    gray_threshold[1] = 255
                    binary_threshold = gray_threshold[:2]
                    print("已重置灰度阈值为全白色背景")
                else:  # lab
                    lab_threshold[0] = 0
                    lab_threshold[1] = 100
                    lab_threshold[2] = -128
                    lab_threshold[3] = 127
                    lab_threshold[4] = -128
                    lab_threshold[5] = 127
                    print("已重置LAB阈值为全白色背景")

            # 保存当前阈值
            elif btn == "save":
                if current_mode == 'lab':
                    # LAB模式保存时需要调整A、B通道的值（减去128）
                    adj_vals = [lab_threshold[i] for i in range(6)]
                    saved_lab_thresholds.append(adj_vals.copy())
                    print(f"已保存LAB阈值: {adj_vals}")
                elif current_mode == 'gray':
                    saved_gray_thresholds.append(gray_threshold[:2].copy())
                    binary_threshold = gray_threshold[:2]
                    print(f"已保存灰度阈值: {gray_threshold[:2]}")

            # 调整滑块
            else:
                idx = int(btn)
                max_params = 2 if current_mode == 'gray' else 6

                if idx >= 6:  # 右侧按钮增加阈值
                    chan_idx = idx - 6
                    if chan_idx < max_params:
                        if current_mode == 'gray':
                            gray_threshold[chan_idx] = min(255, gray_threshold[chan_idx] + 2)
                            binary_threshold = gray_threshold[:2]
                        else:  # lab
                            if chan_idx < 2:  # L通道
                                lab_threshold[chan_idx] = min(100, lab_threshold[chan_idx] + 2)
                            else:  # A, B通道
                                lab_threshold[chan_idx] = min(127, lab_threshold[chan_idx] + 2)
                else:  # 左侧按钮减小阈值
                    chan_idx = idx
                    if chan_idx < max_params:
                        if current_mode == 'gray':
                            gray_threshold[chan_idx] = max(0, gray_threshold[chan_idx] - 2)
                            binary_threshold = gray_threshold[:2]
                        else:  # lab
                            if chan_idx < 2:  # L通道
                                lab_threshold[chan_idx] = max(0, lab_threshold[chan_idx] - 2)
                            else:  # A, B通道
                                lab_threshold[chan_idx] = max(-128, lab_threshold[chan_idx] - 2)

                # 验证阈值逻辑
                validate_threshold()

            time.sleep_ms(100)  # 防止重复触发

    return img

#_____________________________________________主程序________________________________________________
while True:
    try:
        clock.tick()
        os.exitpoint()

        # 串口数据处理
        if uart.any():
            Flag_transform()

        # 定期内存清理，提高性能
        if ticks_ms() % 5000 < 50:  # 每5秒清理一次内存
            gc.collect()

        # 性能优化：减少跳帧以提高检测精度
        frame_skip_count += 1
        if frame_skip_count <= max_frame_skip and Flag not in [1, 7]:
            continue
        frame_skip_count = 0

        # 性能优化：减少不必要的内存分配
        current_time = ticks_ms()
        if current_time - last_detection_time < detection_interval and Flag not in [1, 6, 7]:
            # 对于非关键任务，降低检测频率以提高帧率
            img = sensor.snapshot()
            adaptive_display_image(img)
            continue
        last_detection_time = current_time

        # 实现一个长按屏幕进入阈值编辑模式的效果（仅在非绘画模式下检测）
        if Flag != 6:  # 绘画模式下不检测触摸，避免干扰
            points = tp.read()
            if len(points) > 0:
                touch_counter += 1
                if touch_counter > 10:  # 减少触摸检测时间，提高响应速度
                    if not threshold_adjustment_mode:
                        threshold_adjustment_mode = True
                        Flag = 1
                        # 重置阈值为全白色背景的初始值
                        lab_threshold = [0, 100, -128, 127, -128, 127]  # LAB全范围，显示纯白色
                        gray_threshold = [0, 255]  # 灰度全范围，显示纯白色
                        current_mode = 'lab'  # 默认进入LAB模式
                        print("进入阈值调节模式 - 初始化为纯白色背景")
                        touch_counter = 0  # 重置计数器
                # 减少调试输出，提高性能
                # print(f"触摸位置: {points[0].x}, {points[0].y}")
            else:
                touch_counter -= 2
                touch_counter = max(0, touch_counter)

        # 主要功能逻辑
        if Flag == 0:
            # 400x240分辨率正常显示
            img = sensor.snapshot()
            adaptive_display_image(img)

        elif Flag == 1:
            # 检查是否进入阈值调节模式
            if threshold_adjustment_mode:
                # 执行阈值调节系统（400x120分辨率）
                img = threshold_adjustment_system()
                storage()
                adaptive_display_image(img)

        #E题基本要求(2)：2秒内瞄准靶心，D1≤2cm
        elif Flag == 2:
            goto_target_point_1()

        #E题基本要求(3)：4秒内自动瞄准，D1≤2cm
        elif Flag == 3:
            goto_target_point_2()

        #E题发挥(1)：N=1圈，t≤20s，D1≤2cm
        elif Flag == 4:
            goto_target_point_3()

        #E题发挥(2)：N=2圈，t≤40s，D1≤2cm
        elif Flag == 5:
            goto_target_point_3()

        #发挥部分第三题：基于几何关系的圆环检测
        elif Flag == 6:
            goto_target_point_4()

        #系统监控和调试
        elif Flag == 7:
            goto_target_point_5()

    except Exception as e:
        print(f"主程序错误: {e}")
        # 错误恢复：重置到安全状态
        Flag = 0
        threshold_adjustment_mode = False
        time.sleep_ms(100)