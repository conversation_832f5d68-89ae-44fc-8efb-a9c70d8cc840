# K230智能分辨率切换系统
# 黑框识别自动使用QVGA，其他任务自动使用HD

import time
import os
import math
import gc
from time import ticks_ms
from machine import FPIOA, Pin, UART, TOUCH
from media.sensor import *
from media.display import *
from media.media import *

#______________________________________参数配置___________________________________________

# 任务模式选择
Flag = 1  # 1:QVGA黑框识别, 2:HD色块识别, 0:HD阈值调节

# 分辨率状态
current_resolution = "HD"  # 当前分辨率状态

# 检测阈值
black_threshold = (0, 30, -20, 20, -20, 20)  # 黑色阈值
red_threshold = (30, 100, 15, 127, 15, 127)  # 红色阈值
binary_threshold = 128  # 二值化阈值

# 阈值调节模式
threshold_adjustment_mode = False

#_________________________________________模块初始化_____________________________________________

# 摄像头初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width=800, height=480)
sensor.set_pixformat(Sensor.RGB565)

# 串口初始化
fpioa = FPIOA()
fpioa.set_function(3, FPIOA.UART1_TXD)
fpioa.set_function(4, FPIOA.UART1_RXD)
uart = UART(UART.UART1, 115200)

# 显示初始化
Display.init(Display.ST7701, to_ide=True)
MediaManager.init()
sensor.run()
clock = time.clock()
tp = TOUCH(0)

print("智能分辨率切换系统初始化完成")

#__________________________________________智能分辨率切换函数_____________________________________________

def sensor_QVGA():
    """切换到QVGA分辨率用于黑框识别（高帧率）"""
    global current_resolution
    
    if current_resolution == "QVGA":
        return True  # 已经是QVGA分辨率
    
    try:
        print("切换到QVGA分辨率...")
        sensor.stop()
        time.sleep_ms(30)
        
        sensor.reset()
        sensor.set_framesize(width=320, height=240)
        sensor.set_pixformat(Sensor.RGB565)
        
        sensor.run()
        time.sleep_ms(50)
        
        current_resolution = "QVGA"
        print("QVGA切换完成")
        return True
        
    except Exception as e:
        print(f"QVGA切换失败: {e}")
        return False

def sensor_HD():
    """切换到HD分辨率用于色块识别和阈值调节（高精度）"""
    global current_resolution
    
    if current_resolution == "HD":
        return True  # 已经是HD分辨率
    
    try:
        print("切换到HD分辨率...")
        sensor.stop()
        time.sleep_ms(30)
        
        sensor.reset()
        sensor.set_framesize(width=800, height=480)
        sensor.set_pixformat(Sensor.RGB565)
        
        sensor.run()
        time.sleep_ms(50)
        
        current_resolution = "HD"
        print("HD切换完成")
        return True
        
    except Exception as e:
        print(f"HD切换失败: {e}")
        return False

def get_current_resolution():
    """获取当前分辨率信息"""
    if current_resolution == "HD":
        return 800, 480
    else:
        return 320, 240

#__________________________________________功能函数_____________________________________________

def safe_uart_send(data_list):
    """安全的串口发送"""
    try:
        uart.write(bytes(data_list))
        return True
    except:
        return False

def calculate_distance(x1, y1, x2, y2):
    """计算两点间距离"""
    return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)

def img_bin_rects(img):
    """二值化并查找矩形"""
    try:
        # 转换为灰度图像
        img_gray = img.to_grayscale()
        
        # 二值化
        img_bin = img_gray.binary([(0, binary_threshold)])
        
        # 查找矩形
        rects = img_bin.find_rects(threshold=1000)
        return rects
    except:
        return []

def find_max_rect(rects):
    """找到最大的矩形"""
    if not rects:
        return None
    return max(rects, key=lambda r: r.area())

#__________________________________________主要任务函数_____________________________________________

def qvga_black_frame_detection():
    """QVGA黑框识别 - 高帧率"""
    # 自动切换到QVGA分辨率
    sensor_QVGA()
    
    img = sensor.snapshot()
    width, height = get_current_resolution()
    center_x = width // 2
    center_y = height // 2
    
    # 使用二值化方法检测黑框
    rects = img_bin_rects(img)
    
    if rects:
        max_rect = find_max_rect(rects)
        if max_rect:
            # 计算矩形中心点
            rect_cx = int(sum(p[0] for p in max_rect.corners()) / 4)
            rect_cy = int(sum(p[1] for p in max_rect.corners()) / 4)

            # 绘制矩形中心点和轮廓
            img.draw_circle(rect_cx, rect_cy, 3, color=(255, 0, 0), thickness=2, fill=True)

            corners = max_rect.corners()
            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(0, 255, 0), thickness=2)

            # 计算误差（相对于QVGA中心点）
            e_x = center_x - rect_cx
            e_y = center_y - rect_cy
            
            # 将QVGA坐标转换为HD坐标系用于控制
            e_x_hd = int(e_x * 2.5)  # QVGA->HD X轴缩放
            e_y_hd = int(e_y * 2.0)  # QVGA->HD Y轴缩放
            
            distance = calculate_distance(rect_cx, rect_cy, center_x, center_y)

            # 发送控制信号（使用HD坐标系）
            e_x_high = (e_x_hd >> 8) & 0xFF
            e_x_low = e_x_hd & 0xFF
            e_y_high = (e_y_hd >> 8) & 0xFF
            e_y_low = e_y_hd & 0xFF

            send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
            safe_uart_send(send_lst)

            # 显示信息
            img.draw_string_advanced(5, 5, 10, f"黑框跟踪 QVGA", color=(0, 255, 0))
            img.draw_string_advanced(5, 16, 8, f"中心: ({rect_cx}, {rect_cy})", color=(255, 255, 255))
            img.draw_string_advanced(5, 27, 8, f"误差: ({e_x}, {e_y})", color=(255, 255, 255))
            img.draw_string_advanced(5, 38, 8, f"距离: {distance:.1f}px", color=(255, 255, 255))
    else:
        img.draw_string_advanced(5, 5, 12, "搜索黑框中... QVGA", color=(255, 255, 0))
    
    # 显示帧率信息
    fps = clock.fps()
    img.draw_string_advanced(5, height-15, 8, f"FPS: {fps:.1f} {width}x{height}", color=(255, 255, 255))
    
    # 绘制中心十字线
    img.draw_cross(center_x, center_y, color=(0, 0, 255), size=6, thickness=1)
    
    Display.show_image(img)

def hd_color_detection():
    """HD色块识别 - 高精度"""
    # 自动切换到HD分辨率
    sensor_HD()
    
    img = sensor.snapshot()
    width, height = get_current_resolution()
    
    # 查找红色色块
    red_blobs = img.find_blobs([red_threshold], pixels_threshold=50, area_threshold=50)
    
    if red_blobs:
        max_blob = max(red_blobs, key=lambda b: b.pixels())
        
        # 绘制最大的红色色块
        img.draw_rectangle(max_blob.rect(), color=(255, 0, 0), thickness=3)
        img.draw_cross(max_blob.cx(), max_blob.cy(), color=(255, 0, 0), size=15, thickness=3)
        
        # 计算误差
        e_x = 400 - max_blob.cx()
        e_y = 240 - max_blob.cy()
        distance = calculate_distance(max_blob.cx(), max_blob.cy(), 400, 240)
        
        # 发送控制信号
        e_x_high = (e_x >> 8) & 0xFF
        e_x_low = e_x & 0xFF
        e_y_high = (e_y >> 8) & 0xFF
        e_y_low = e_y & 0xFF
        
        send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
        safe_uart_send(send_lst)
        
        # 显示信息
        img.draw_string_advanced(10, 10, 16, f"红色跟踪 HD", color=(255, 0, 0))
        img.draw_string_advanced(10, 30, 16, f"中心: ({max_blob.cx()}, {max_blob.cy()})", color=(255, 255, 255))
        img.draw_string_advanced(10, 50, 16, f"误差: ({e_x}, {e_y})", color=(255, 255, 255))
        img.draw_string_advanced(10, 70, 16, f"距离: {distance:.1f}px", color=(255, 255, 255))
    else:
        img.draw_string_advanced(10, 10, 20, "搜索红色目标... HD", color=(255, 255, 0))
    
    # 显示帧率信息
    fps = clock.fps()
    img.draw_string_advanced(10, 90, 16, f"FPS: {fps:.1f} {width}x{height}", color=(255, 255, 255))
    
    # 绘制中心十字线
    img.draw_cross(400, 240, color=(0, 0, 255), size=15, thickness=2)
    
    Display.show_image(img)

def hd_threshold_adjustment():
    """HD阈值调节 - 高精度"""
    # 自动切换到HD分辨率
    sensor_HD()
    
    img = sensor.snapshot()
    width, height = get_current_resolution()
    
    # 显示原图和阈值信息
    img.draw_string_advanced(10, 10, 20, f"HD阈值调节 {width}x{height}", color=(255, 255, 255))
    img.draw_string_advanced(10, 35, 16, f"黑色阈值: {black_threshold}", color=(255, 255, 255))
    img.draw_string_advanced(10, 55, 16, f"红色阈值: {red_threshold}", color=(255, 255, 255))
    img.draw_string_advanced(10, 75, 16, f"二值化阈值: {binary_threshold}", color=(255, 255, 255))
    
    # 查找各种颜色用于阈值调节
    black_blobs = img.find_blobs([black_threshold], merge=False, pixels_threshold=100)
    red_blobs = img.find_blobs([red_threshold], merge=False, pixels_threshold=50)
    
    # 绘制检测结果
    if black_blobs:
        for i, blob in enumerate(black_blobs[:3]):
            img.draw_rectangle(blob.rect(), color=(255, 255, 0), thickness=2)
            img.draw_string_advanced(blob.x(), blob.y()-15, 12, f"黑{i+1}", color=(255, 255, 0))
    
    if red_blobs:
        for i, blob in enumerate(red_blobs[:3]):
            img.draw_rectangle(blob.rect(), color=(0, 255, 255), thickness=2)
            img.draw_string_advanced(blob.x(), blob.y()-15, 12, f"红{i+1}", color=(0, 255, 255))
    
    # 显示性能信息
    fps = clock.fps()
    img.draw_string_advanced(10, 95, 16, f"FPS: {fps:.1f}", color=(255, 255, 255))
    img.draw_string_advanced(10, 115, 16, f"黑色: {len(black_blobs) if black_blobs else 0} 红色: {len(red_blobs) if red_blobs else 0}", color=(255, 255, 255))
    img.draw_string_advanced(10, 135, 16, "高精度阈值调节模式", color=(0, 255, 0))
    
    Display.show_image(img)

#_____________________________________________主程序________________________________________________
while True:
    try:
        clock.tick()
        os.exitpoint()
        
        if Flag == 1:
            # QVGA黑框识别（高帧率）
            qvga_black_frame_detection()
        elif Flag == 2:
            # HD色块识别（高精度）
            hd_color_detection()
        elif Flag == 0:
            # HD阈值调节（高精度）
            hd_threshold_adjustment()
        else:
            # 默认显示
            img = sensor.snapshot()
            width, height = get_current_resolution()
            img.draw_string_advanced(10, 10, 20, f"智能分辨率切换系统", color=(255, 255, 255))
            img.draw_string_advanced(10, 35, 16, f"当前: {current_resolution} {width}x{height}", color=(255, 255, 255))
            img.draw_string_advanced(10, 55, 16, f"Flag=1: QVGA黑框识别", color=(255, 255, 0))
            img.draw_string_advanced(10, 75, 16, f"Flag=2: HD色块识别", color=(255, 255, 0))
            img.draw_string_advanced(10, 95, 16, f"Flag=0: HD阈值调节", color=(255, 255, 0))
            img.draw_string_advanced(10, 115, 16, f"当前Flag={Flag}", color=(0, 255, 0))
            
            fps = clock.fps()
            img.draw_string_advanced(10, 135, 16, f"FPS: {fps:.1f}", color=(255, 255, 255))
            
            Display.show_image(img)
        
        # 定期内存清理
        if ticks_ms() % 5000 < 50:
            gc.collect()
            
    except Exception as e:
        print(f"程序错误: {e}")
        time.sleep_ms(100)
