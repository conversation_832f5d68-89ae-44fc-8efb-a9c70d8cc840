# E题 - 简易自动瞄准装置（K230版本）
# 专门针对E题要求优化的简化版本

import time
import os
import math
import gc
from time import ticks_ms
from machine import FPIOA, Pin, UART, TOUCH
from media.sensor import *
from media.display import *
from media.media import *

#______________________________________E题专用参数配置___________________________________________

# 任务模式选择
Flag = 2  # 2:基本要求瞄准靶心, 3:4秒自动瞄准, 6:同步画圆

# 目标检测参数（50cm距离，A4靶纸）
pixel_per_cm = 10  # 像素-厘米转换比例（需要实际标定）
target_center_tolerance = 2.0  # 靶心精度要求：≤2cm
target_6cm_radius = 6 * pixel_per_cm  # 6cm圆的像素半径

# 红色阈值（LAB色彩空间）
red_threshold = (30, 100, 15, 127, 15, 127)

# 瞄准控制参数
precision_threshold = target_center_tolerance * pixel_per_cm  # 精度阈值（像素）
aiming_timeout = 4000  # 瞄准超时时间（4秒）

# 同步画圆参数
drawing_active = False
car_position_angle = 0  # 小车位置角度
drawing_start_time = 0

#_________________________________________模块初始化_____________________________________________

# 摄像头初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width=800, height=480)
sensor.set_pixformat(Sensor.RGB565)

# 串口初始化
fpioa = FPIOA()
fpioa.set_function(3, FPIOA.UART1_TXD)
fpioa.set_function(4, FPIOA.UART1_RXD)
uart = UART(UART.UART1, 115200)

# 显示初始化
Display.init(Display.ST7701, to_ide=True)
MediaManager.init()
sensor.run()
clock = time.clock()

print("E题自动瞄准装置初始化完成")

#__________________________________________核心功能函数_____________________________________________

def safe_uart_send(data_list):
    """安全的串口发送"""
    try:
        uart.write(bytes(data_list))
        return True
    except:
        return False

def fire_laser():
    """发射激光"""
    laser_command = [0x2c, 0x13, 0x01, 0x5B]
    return safe_uart_send(laser_command)

def calculate_circularity(blob):
    """计算圆形度"""
    try:
        area = blob.pixels()
        perimeter = blob.perimeter()
        if perimeter == 0:
            return 0
        return (4 * math.pi * area) / (perimeter * perimeter)
    except:
        return 0

def detect_target_center(red_blobs):
    """检测靶心（红色小点）"""
    for blob in red_blobs:
        if 1 <= blob.pixels() <= 50:  # 靶心很小
            circularity = calculate_circularity(blob)
            if circularity > 0.6:  # 靶心应该很圆
                # 检查是否在中心区域
                center_distance = math.sqrt((blob.cx() - 400)**2 + (blob.cy() - 240)**2)
                if center_distance < 200:
                    return blob
    return None

def detect_6cm_circle(red_blobs):
    """检测6cm红色圆"""
    best_circle = None
    best_score = 0
    
    for blob in red_blobs:
        if blob.pixels() < 50 or blob.pixels() > 5000:
            continue
        
        # 计算半径
        area = blob.pixels()
        estimated_radius = math.sqrt(area / math.pi)
        
        # 检查是否接近6cm
        radius_diff = abs(estimated_radius - target_6cm_radius)
        if radius_diff <= target_6cm_radius * 0.4:  # 40%容差
            circularity = calculate_circularity(blob)
            score = circularity * (1 - radius_diff / target_6cm_radius)
            
            if score > best_score:
                best_score = score
                best_circle = blob
    
    return best_circle

def precise_aiming_control(target_x, target_y):
    """精确瞄准控制"""
    e_x = 400 - target_x  # 屏幕中心为瞄准点
    e_y = 240 - target_y
    
    distance_pixels = math.sqrt(e_x**2 + e_y**2)
    distance_cm = distance_pixels / pixel_per_cm
    
    is_precise = distance_cm <= target_center_tolerance
    return is_precise, e_x, e_y, distance_cm

def send_aiming_command(e_x, e_y):
    """发送瞄准控制指令"""
    e_x_high = (int(e_x) >> 8) & 0xFF
    e_x_low = int(e_x) & 0xFF
    e_y_high = (int(e_y) >> 8) & 0xFF
    e_y_low = int(e_y) & 0xFF
    
    send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
    return safe_uart_send(send_lst)

#__________________________________________E题主要功能函数_____________________________________________

def e_basic_aiming():
    """E题基本要求：瞄准靶心"""
    img = sensor.snapshot()
    
    # 检测红色区域
    red_blobs = img.find_blobs([red_threshold], pixels_threshold=1, area_threshold=1)
    
    if red_blobs:
        # 检测靶心
        target_center = detect_target_center(red_blobs)
        
        if target_center:
            target_x, target_y = target_center.cx(), target_center.cy()
            
            # 精确瞄准控制
            is_precise, e_x, e_y, distance_cm = precise_aiming_control(target_x, target_y)
            
            # 绘制靶心标记
            img.draw_circle(target_x, target_y, 15, color=(255, 0, 0), thickness=3)
            img.draw_cross(target_x, target_y, color=(255, 0, 0), size=25, thickness=3)
            
            # 显示信息
            img.draw_string_advanced(10, 10, 20, f"靶心: ({target_x}, {target_y})", color=(255, 0, 0))
            img.draw_string_advanced(10, 35, 20, f"误差: {distance_cm:.2f}cm", color=(255, 0, 0))
            
            if is_precise:
                # 达到精度要求，发射激光
                fire_laser()
                img.draw_string_advanced(10, 60, 24, "激光发射! 命中!", color=(0, 255, 0))
            else:
                # 发送瞄准控制信号
                send_aiming_command(e_x, e_y)
                img.draw_string_advanced(10, 60, 20, "正在瞄准...", color=(255, 255, 0))
        else:
            img.draw_string_advanced(10, 10, 24, "搜索靶心中...", color=(255, 255, 0))
    else:
        img.draw_string_advanced(10, 10, 24, "未检测到红色目标", color=(255, 0, 0))
    
    # 绘制瞄准十字线
    img.draw_cross(400, 240, color=(0, 0, 255), size=40, thickness=2)
    img.draw_circle(400, 240, int(precision_threshold), color=(0, 0, 255), thickness=1)
    
    Display.show_image(img)

def e_sync_circle_drawing():
    """E题发挥第三问：同步画圆"""
    global drawing_active, drawing_start_time, car_position_angle
    
    img = sensor.snapshot()
    
    # 检测红色区域
    red_blobs = img.find_blobs([red_threshold], pixels_threshold=50, area_threshold=50)
    
    if red_blobs:
        # 检测6cm圆
        target_6cm_circle = detect_6cm_circle(red_blobs)
        
        if target_6cm_circle:
            circle_x, circle_y = target_6cm_circle.cx(), target_6cm_circle.cy()
            circle_r = math.sqrt(target_6cm_circle.pixels() / math.pi)
            
            # 绘制6cm圆标记
            img.draw_rectangle(target_6cm_circle.rect(), color=(255, 0, 0), thickness=3)
            img.draw_circle(circle_x, circle_y, int(circle_r), color=(255, 0, 0), thickness=2)
            
            # 启动同步画圆
            current_time = ticks_ms()
            if not drawing_active:
                drawing_start_time = current_time
                drawing_active = True
            
            # 计算小车角度（模拟：20秒1圈）
            elapsed_time = current_time - drawing_start_time
            car_position_angle = (elapsed_time / 20000.0) * 360.0
            car_position_angle = car_position_angle % 360
            
            # 计算激光目标点（在6cm圆上）
            laser_angle_rad = math.radians(car_position_angle)
            laser_x = circle_x + circle_r * math.cos(laser_angle_rad)
            laser_y = circle_y + circle_r * math.sin(laser_angle_rad)
            
            # 瞄准控制
            e_x = laser_x - 400
            e_y = laser_y - 240
            send_aiming_command(e_x, e_y)
            
            # 连续发光
            fire_laser()
            
            # 绘制激光目标点
            img.draw_cross(int(laser_x), int(laser_y), color=(0, 255, 255), size=15, thickness=3)
            img.draw_line(400, 240, int(laser_x), int(laser_y), color=(0, 255, 255), thickness=2)
            
            # 显示信息
            img.draw_string_advanced(10, 10, 16, f"6cm圆: ({circle_x}, {circle_y})", color=(255, 0, 0))
            img.draw_string_advanced(10, 30, 16, f"小车角度: {car_position_angle:.1f}°", color=(255, 255, 0))
            img.draw_string_advanced(10, 50, 16, f"激光目标: ({int(laser_x)}, {int(laser_y)})", color=(0, 255, 255))
            img.draw_string_advanced(10, 70, 20, "同步画圆中...", color=(0, 255, 0))
        else:
            img.draw_string_advanced(10, 10, 24, "搜索6cm红色圆...", color=(255, 255, 0))
            drawing_active = False
    else:
        img.draw_string_advanced(10, 10, 24, "未检测到红色目标", color=(255, 0, 0))
        drawing_active = False
    
    Display.show_image(img)

#_____________________________________________主程序________________________________________________
while True:
    try:
        clock.tick()
        os.exitpoint()
        
        if Flag == 2 or Flag == 3:
            # E题基本要求：瞄准靶心
            e_basic_aiming()
        elif Flag == 6:
            # E题发挥第三问：同步画圆
            e_sync_circle_drawing()
        else:
            # 默认显示
            img = sensor.snapshot()
            img.draw_string_advanced(10, 10, 24, f"E题模式 Flag={Flag}", color=(255, 255, 255))
            img.draw_string_advanced(10, 40, 20, "2:瞄准靶心 3:自动瞄准 6:同步画圆", color=(255, 255, 255))
            Display.show_image(img)
        
        # 定期内存清理
        if ticks_ms() % 5000 < 50:
            gc.collect()
            
    except Exception as e:
        print(f"程序错误: {e}")
        time.sleep_ms(100)
