#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
树莓派云台瞄准控制系统
功能：
1. 视觉检测黑框和靶心
2. 通过串口控制单片机驱动舵机
3. 实现2秒和4秒瞄准模式
4. 针对树莓派性能优化
"""

import cv2
import numpy as np
import serial
import time
import threading
from queue import Queue
import json

# 移除PID控制器类，PID控制交给单片机处理

class GimbalControlProtocol:
    """云台控制通信协议"""
    def __init__(self):
        self.HEADER = 0xAA
        self.FOOTER = 0xAB
        
        # 命令类型
        self.CMD_GIMBAL_MOVE = 0x10    # 云台移动命令（废弃，改用偏差值）
        self.CMD_GIMBAL_STOP = 0x11    # 云台停止命令
        self.CMD_FIRE = 0x12           # 发射命令
        self.CMD_TARGET_INFO = 0x13    # 目标信息
        self.CMD_MODE_SWITCH = 0x14    # 模式切换
        self.CMD_TARGET_ERROR = 0x15   # 目标偏差值（新增，让单片机做PID）
    
    def create_gimbal_move_packet(self, delta_x, delta_y, speed=50):
        """
        创建云台移动数据包
        格式: 0xAA + 0x10 + delta_x(2字节) + delta_y(2字节) + speed(1字节) + 0xAB
        """
        # 限制移动范围 (-1000 到 1000)
        delta_x = max(-1000, min(1000, int(delta_x)))
        delta_y = max(-1000, min(1000, int(delta_y)))
        speed = max(1, min(100, int(speed)))
        
        packet = bytearray()
        packet.append(self.HEADER)
        packet.append(self.CMD_GIMBAL_MOVE)
        
        # delta_x (2字节，有符号)
        if delta_x >= 0:
            packet.extend(delta_x.to_bytes(2, 'big'))
        else:
            packet.extend((65536 + delta_x).to_bytes(2, 'big'))
            
        # delta_y (2字节，有符号)  
        if delta_y >= 0:
            packet.extend(delta_y.to_bytes(2, 'big'))
        else:
            packet.extend((65536 + delta_y).to_bytes(2, 'big'))
            
        packet.append(speed)
        packet.append(self.FOOTER)
        
        return bytes(packet)
    
    def create_stop_packet(self):
        """创建停止命令包"""
        packet = bytearray([self.HEADER, self.CMD_GIMBAL_STOP, 0x00, 0x00, 0x00, 0x00, self.FOOTER])
        return bytes(packet)
    
    def create_fire_packet(self):
        """创建发射命令包"""
        packet = bytearray([self.HEADER, self.CMD_FIRE, 0x00, 0x00, 0x00, 0x00, self.FOOTER])
        return bytes(packet)
    
    def create_target_info_packet(self, target_x, target_y, frame_center_x, frame_center_y):
        """发送目标位置信息"""
        packet = bytearray()
        packet.append(self.HEADER)
        packet.append(self.CMD_TARGET_INFO)
        
        packet.extend(target_x.to_bytes(2, 'big'))
        packet.extend(target_y.to_bytes(2, 'big'))
        packet.extend(frame_center_x.to_bytes(2, 'big'))
        packet.extend(frame_center_y.to_bytes(2, 'big'))
        
        packet.append(self.FOOTER)
        return bytes(packet)

    def create_target_error_packet(self, error_x, error_y, mode):
        """
        创建目标偏差数据包（方案一：发送偏差值让单片机做PID）
        格式: 0xAA + 0x15 + error_x(2字节,有符号) + error_y(2字节,有符号) + mode(1字节) + 0xAB
        """
        # 限制偏差范围
        error_x = max(-1000, min(1000, int(error_x)))
        error_y = max(-1000, min(1000, int(error_y)))

        packet = bytearray()
        packet.append(self.HEADER)
        packet.append(self.CMD_TARGET_ERROR)

        # error_x (2字节，有符号)
        if error_x >= 0:
            packet.extend(error_x.to_bytes(2, 'big'))
        else:
            packet.extend((65536 + error_x).to_bytes(2, 'big'))

        # error_y (2字节，有符号)
        if error_y >= 0:
            packet.extend(error_y.to_bytes(2, 'big'))
        else:
            packet.extend((65536 + error_y).to_bytes(2, 'big'))

        packet.append(mode)  # 6=快速模式, 7=精确模式
        packet.append(self.FOOTER)

        return bytes(packet)

class RaspberryPiVisionSystem:
    """树莓派视觉系统"""
    def __init__(self, camera_id=0):
        # 摄像头初始化
        self.cap = cv2.VideoCapture(camera_id)
        self.init_camera()
        
        # 图像参数
        self.frame_width = 640
        self.frame_height = 480
        self.frame_center_x = self.frame_width // 2
        self.frame_center_y = self.frame_height // 2
        
        # 检测参数
        self.binary_threshold = 80
        self.min_area = 200  # 降低最小面积以支持远距离检测
        self.max_area = 30000

        # 远距离检测优化参数
        self.distance_adaptive = True  # 启用距离自适应
        self.last_detection_area = None  # 记录上次检测到的面积

        # 稳定性滤波参数
        self.detection_history = []  # 检测历史
        self.history_size = 5  # 历史记录大小
        self.stability_threshold = 0.7  # 稳定性阈值
        self.last_stable_center = None  # 上次稳定的中心点

        # 多尺度检测参数
        self.enable_multiscale = True  # 启用多尺度检测
        self.scale_factors = [1.0, 0.8, 1.2]  # 不同的缩放因子
        
        # 预分配内存缓冲区（性能优化）
        self.gray_buffer = np.zeros((self.frame_height, self.frame_width), dtype=np.uint8)
        self.binary_buffer = np.zeros((self.frame_height, self.frame_width), dtype=np.uint8)
        self.small_buffer = np.zeros((240, 320), dtype=np.uint8)
        
        # 历史检测记录
        self.last_target_pos = None
        
    def init_camera(self):
        """初始化摄像头参数"""
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        self.cap.set(cv2.CAP_PROP_FPS, 30)
        
        # 固定曝光和白平衡
        self.cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)
        self.cap.set(cv2.CAP_PROP_EXPOSURE, -6)
        self.cap.set(cv2.CAP_PROP_AUTO_WB, 0)
        
        print("[摄像头] 初始化完成")
    
    def detect_black_frame_fast(self, frame):
        """
        快速黑框检测（2秒模式用）
        优化：降低分辨率，简化算法
        """
        # 缩小图像提速
        small_frame = cv2.resize(frame, (320, 240))
        cv2.cvtColor(small_frame, cv2.COLOR_BGR2GRAY, dst=self.small_buffer)
        
        # 简化二值化
        _, binary = cv2.threshold(self.small_buffer, self.binary_threshold, 255, cv2.THRESH_BINARY_INV)
        
        # 快速轮廓检测
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 200 < area < 8000:  # 缩放后的面积范围
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = max(w, h) / min(w, h) if min(w, h) > 0 else 0
                
                if 0.8 <= aspect_ratio <= 2.0:
                    # 坐标映射回原图
                    center_x = int((x + w//2) * 2)  # 320->640
                    center_y = int((y + h//2) * 2)  # 240->480
                    return center_x, center_y
        
        return None, None

    def add_detection_to_history(self, center):
        """添加检测结果到历史记录"""
        if center is not None:
            self.detection_history.append(center)
        else:
            self.detection_history.append(None)

        # 保持历史记录大小
        if len(self.detection_history) > self.history_size:
            self.detection_history.pop(0)

    def get_stable_center(self):
        """获取稳定的中心点"""
        if len(self.detection_history) < 3:
            return None

        # 过滤掉None值
        valid_centers = [c for c in self.detection_history if c is not None]
        if len(valid_centers) < 2:
            return None

        # 计算中心点的稳定性
        if len(valid_centers) >= 3:
            # 计算最近几个点的平均位置
            recent_centers = valid_centers[-3:]
            avg_x = sum(c[0] for c in recent_centers) / len(recent_centers)
            avg_y = sum(c[1] for c in recent_centers) / len(recent_centers)

            # 检查稳定性（点之间的距离变化）
            max_distance = 0
            for center in recent_centers:
                distance = ((center[0] - avg_x) ** 2 + (center[1] - avg_y) ** 2) ** 0.5
                max_distance = max(max_distance, distance)

            # 如果变化小于阈值，认为是稳定的
            if max_distance < 30:  # 30像素的稳定性阈值
                self.last_stable_center = (int(avg_x), int(avg_y))
                return self.last_stable_center

        return self.last_stable_center

    def detect_red_target_in_roi(self, roi):
        """在ROI内检测红色靶心"""
        hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)
        
        # 红色HSV范围
        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([170, 50, 50])
        upper_red2 = np.array([180, 255, 255])
        
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        mask = mask1 + mask2
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            largest_contour = max(contours, key=cv2.contourArea)
            area = cv2.contourArea(largest_contour)
            
            if area > 50:
                M = cv2.moments(largest_contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    return cx, cy
        
        return None, None
    
    def detect_target_precise(self, frame):
        """
        精确目标检测（4秒模式用）
        先检测黑框，再在黑框内检测红色靶心
        """
        # 1. 快速定位黑框
        frame_center = self.detect_black_frame_fast(frame)
        if frame_center[0] is None:
            return None, None
        
        fx, fy = frame_center
        
        # 2. 在黑框周围创建ROI
        roi_size = 200
        x1 = max(0, fx - roi_size//2)
        y1 = max(0, fy - roi_size//2)
        x2 = min(self.frame_width, fx + roi_size//2)
        y2 = min(self.frame_height, fy + roi_size//2)
        
        roi = frame[y1:y2, x1:x2]
        
        # 3. 在ROI内检测红色靶心
        target_center = self.detect_red_target_in_roi(roi)
        if target_center[0] is not None and target_center[1] is not None:
            global_x = target_center[0] + x1
            global_y = target_center[1] + y1
            return global_x, global_y
        
        # 4. 没找到靶心，返回黑框中心
        return fx, fy

    def detect_black_frame_with_binary_debug(self, frame):
        """
        带二值化调试的黑框检测（用于阈值调节）
        返回: (center_x, center_y, binary_image, bbox_info)
        bbox_info: (x1, y1, x2, y2, contour) 黑框的边界框坐标和轮廓
        """
        # 使用优化的检测方法
        center_x, center_y, best_contour = self.detect_black_frame_laser_tracker_style(frame)

        # 生成二值化调试图像
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # 远距离检测优化：边缘增强 + 降噪
            gray_blurred = cv2.GaussianBlur(gray, (3, 3), 0)

            # 边缘增强
            kernel_sharpen = np.array([[-1,-1,-1],
                                      [-1, 9,-1],
                                      [-1,-1,-1]])
            gray_enhanced = cv2.filter2D(gray_blurred, -1, kernel_sharpen)
            gray_final = cv2.addWeighted(gray_blurred, 0.7, gray_enhanced, 0.3, 0)

            # 使用增强后的图像进行二值化
            _, binary_display = cv2.threshold(gray_final, self.binary_threshold, 255, cv2.THRESH_BINARY_INV)

            # 调整到显示尺寸
            binary_display = cv2.resize(binary_display, (640, 480))

        except Exception as e:
            print(f"生成二值化调试图像失败: {e}")
            binary_display = np.zeros((480, 640), dtype=np.uint8)

        # 构建边界框信息
        bbox_info = None
        if center_x is not None and best_contour is not None:
            try:
                # 计算轮廓的精确边界
                contour_points = best_contour.reshape(-1, 2)
                min_x = np.min(contour_points[:, 0])
                max_x = np.max(contour_points[:, 0])
                min_y = np.min(contour_points[:, 1])
                max_y = np.max(contour_points[:, 1])

                # 构建包含轮廓信息的边界框 (x1, y1, x2, y2, contour)
                bbox_info = (int(min_x), int(min_y), int(max_x), int(max_y), best_contour)

            except Exception as e:
                print(f"构建边界框信息失败: {e}")
                bbox_info = None

        return center_x, center_y, binary_display, bbox_info

    def detect_black_frame_laser_tracker_style(self, frame):
        """
        使用laser_tracker风格的精确黑框检测（完全贴合边缘）
        返回: (center_x, center_y, contour) 中心点和精确轮廓
        """
        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # 远距离检测优化：边缘增强 + 降噪
        # 1. 轻微高斯模糊减少噪声
        gray_blurred = cv2.GaussianBlur(gray, (3, 3), 0)

        # 2. 对于远距离小目标，增加边缘增强
        kernel_sharpen = np.array([[-1,-1,-1],
                                  [-1, 9,-1],
                                  [-1,-1,-1]])
        gray_enhanced = cv2.filter2D(gray_blurred, -1, kernel_sharpen)

        # 3. 混合原图和增强图，保持细节
        gray_final = cv2.addWeighted(gray_blurred, 0.7, gray_enhanced, 0.3, 0)

        # 使用增强后的图像进行二值化
        _, binary = cv2.threshold(gray_final, self.binary_threshold, 255, cv2.THRESH_BINARY_INV)

        # 如果固定阈值检测不到目标，尝试自适应阈值
        contours_test, _ = cv2.findContours(binary.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        valid_contours = [c for c in contours_test if self.min_area <= cv2.contourArea(c) <= self.max_area]

        if len(valid_contours) == 0:
            # 尝试自适应阈值（针对远距离优化）
            binary_adaptive = cv2.adaptiveThreshold(gray_final, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                                   cv2.THRESH_BINARY_INV, 11, 2)
            binary = binary_adaptive

            # 重新检测
            contours_adaptive, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            valid_adaptive = [c for c in contours_adaptive if self.min_area <= cv2.contourArea(c) <= self.max_area]

            # 如果自适应阈值也检测不到，尝试多个阈值
            if len(valid_adaptive) == 0:
                for threshold_offset in [-20, -10, 10, 20]:
                    test_threshold = max(10, min(255, self.binary_threshold + threshold_offset))
                    _, binary_test = cv2.threshold(gray_final, test_threshold, 255, cv2.THRESH_BINARY_INV)
                    contours_test, _ = cv2.findContours(binary_test, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                    valid_test = [c for c in contours_test if self.min_area <= cv2.contourArea(c) <= self.max_area]
                    if len(valid_test) > 0:
                        binary = binary_test
                        print(f"使用调整阈值: {test_threshold}")
                        break

        # 使用较小的核进行形态学操作，避免连接不相关的区域
        # 针对远距离小目标优化
        kernel_small = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        kernel_medium = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))

        # 先用小核去除小噪声，保留小目标
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel_small)
        # 再用中等核填充空洞
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel_medium)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        print(f"总共找到 {len(contours)} 个轮廓")

        if not contours:
            print("没有找到任何轮廓")
            return None, None, None

        # 移植laser_tracker的轮廓筛选逻辑（增强远距离检测）
        candidates = []
        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)
            print(f"轮廓 {i}: 面积={area:.0f}")

            # 轮廓近似，减少噪声影响
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            contour = approx  # 使用近似后的轮廓

            # 适配不同尺寸的黑框（降低最小面积以支持远距离检测）
            if area < 200 or area > 100000:
                print(f"轮廓 {i}: 面积不符合要求，跳过")
                continue

            # 计算边界矩形
            x, y, w, h = cv2.boundingRect(contour)

            # 长宽比检查
            aspect_ratio = float(w) / h if h > 0 else 0
            if aspect_ratio < 0.3 or aspect_ratio > 4.0:
                print(f"轮廓 {i}: 长宽比不符合要求，跳过")
                continue

            # 周长检查
            perimeter = cv2.arcLength(contour, True)
            if perimeter < 100 or perimeter > 3000:
                print(f"轮廓 {i}: 周长不符合要求，跳过")
                continue

            # 形状筛选：严格排除三角形，优先矩形，其次多边形
            epsilon = 0.02 * perimeter
            approx = cv2.approxPolyDP(contour, epsilon, True)

            # 形状筛选：严格排除三角形，优先矩形
            vertex_count = len(approx)

            # 直接跳过三角形，不给任何机会
            if vertex_count == 3:
                print(f"轮廓 {i}: 跳过三角形，面积={area:.0f}")
                continue

            print(f"轮廓 {i}: {vertex_count}边形，面积={area:.0f}，通过形状筛选")

            shape_bonus = 0
            if vertex_count == 4:
                # 四边形：可能是矩形，给予大奖励
                shape_bonus = 50
                print(f"轮廓 {i}: 四边形，大奖励评分: {shape_bonus}")
            elif 5 <= vertex_count <= 8:
                # 多边形：中等评分
                shape_bonus = 20
            else:
                # 其他形状：小奖励
                shape_bonus = 10

            # 计算几何特征
            rect_area = w * h
            extent = float(area) / rect_area if rect_area > 0 else 0
            aspect_ratio = max(w, h) / min(w, h) if min(w, h) > 0 else 0

            # 矩形度检查 - 对四边形放宽要求
            # 矩形度检查 - 对四边形放宽要求
            if vertex_count == 4:
                # 四边形：放宽矩形度要求
                if extent < 0.03 or extent > 0.9:
                    print(f"轮廓 {i}: 四边形，矩形度不符合要求，跳过")
                    continue
            else:
                # 其他形状：严格要求
                if extent < 0.05 or extent > 0.8:
                    print(f"轮廓 {i}: 其他形状，矩形度不符合要求，跳过")
                    continue

            # 额外的矩形特征检测
            if vertex_count == 4:
                # 检查是否接近矩形
                # 计算四个角的角度
                approx_points = approx.reshape(-1, 2)
                angles = []
                for i in range(4):
                    p1 = approx_points[i]
                    p2 = approx_points[(i+1) % 4]
                    p3 = approx_points[(i+2) % 4]

                    # 计算角度
                    v1 = p1 - p2
                    v2 = p3 - p2
                    cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-6)
                    angle = np.arccos(np.clip(cos_angle, -1, 1)) * 180 / np.pi
                    angles.append(angle)

                # 检查是否有接近90度的角
                right_angles = sum(1 for angle in angles if 70 <= angle <= 110)
                if right_angles >= 2:
                    shape_bonus += 30  # 额外奖励有直角的四边形
                    print(f"轮廓 {i}: 四边形，直角数量={right_angles}，额外奖励30分")

            # 凸性检查
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            solidity = float(area) / hull_area if hull_area > 0 else 0
            if solidity < 0.6:
                print(f"轮廓 {i}: 凸性不符合要求，跳过")
                continue

            # 评分系统
            score = 0

            # 面积评分（适应远距离小目标，增强远距离权重）
            if 5000 <= area <= 50000:  # 近距离理想大小
                score += 50
            elif 2000 <= area <= 80000:  # 中距离可接受大小
                score += 35
            elif 500 <= area <= 5000:   # 远距离小目标
                score += 45  # 提高远距离小目标分数
            elif 200 <= area <= 2000:   # 很远距离的小目标
                score += 40  # 提高很远距离目标分数
            elif 100 <= area <= 500:    # 极远距离目标
                score += 35  # 新增极远距离支持
            else:
                score += 5

            # 长宽比评分（远距离时形状可能变形，放宽要求）
            if 0.7 <= aspect_ratio <= 1.5:  # 理想正方形
                score += 40
            elif 0.5 <= aspect_ratio <= 2.5:  # 可接受范围（放宽）
                score += 30
            elif 0.3 <= aspect_ratio <= 3.0:  # 远距离变形容忍范围
                score += 20
            else:
                score += 5

            # 矩形度评分
            if 0.3 <= extent <= 0.7:
                score += 30
            elif 0.1 <= extent <= 0.8:
                score += 20
            else:
                score += 5

            # 凸性评分
            if solidity >= 0.8:
                score += 20
            elif solidity >= 0.6:
                score += 15
            else:
                score += 5

            # 添加形状奖励/惩罚
            score += shape_bonus

            print(f"轮廓 {i}: 评分: 面积={area:.0f}, 形状={vertex_count}边形, 总分={score:.0f}")

            # 使用评分阈值筛选（大幅降低阈值以便观察效果）
            if score >= 30:
                candidates.append({
                    'contour': contour,
                    'score': score,
                    'area': area,
                    'center': (x + w//2, y + h//2),
                    'bbox': (x, y, w, h),
                    'aspect_ratio': aspect_ratio,
                    'extent': extent,
                    'solidity': solidity
                })

        # 如果没有找到候选者，尝试使用历史稳定点
        if not candidates:
            print(f"没有找到符合条件的候选者！")
            self.add_detection_to_history(None)
            stable_center = self.get_stable_center()
            if stable_center is not None:
                print(f"使用历史稳定点: {stable_center}")
                return stable_center[0], stable_center[1], None
            print("也没有历史稳定点可用")
            return None, None, None

        # 按得分排序，选择最佳候选者
        candidates.sort(key=lambda x: x['score'], reverse=True)
        best_candidate = candidates[0]
        best_contour = best_candidate['contour']

        # 计算中心点
        cx, cy = best_candidate['center']

        # 添加到历史记录
        self.add_detection_to_history((cx, cy))

        # 获取稳定的中心点
        stable_center = self.get_stable_center()
        if stable_center is not None:
            cx, cy = stable_center

        # 记录检测到的面积，用于距离自适应
        if self.distance_adaptive:
            current_area = best_candidate['area']
            self.last_detection_area = current_area

            # 如果检测到的目标很小，可能是远距离，动态调整参数
            if current_area < 1000:
                print(f"检测到远距离小目标，面积: {current_area}")

        print(f"laser_tracker风格检测: 中心({cx}, {cy}), 评分={best_candidate['score']:.0f}, 面积={best_candidate['area']:.0f}")

        return cx, cy, best_contour

    def detect_target_with_bbox(self, frame):
        """
        检测目标并返回边界框信息（用于显示绿色框）
        返回: (target_x, target_y, black_bbox, red_bbox)
        """
        # 1. 使用laser_tracker风格的精确黑框检测
        fx, fy, best_contour = self.detect_black_frame_laser_tracker_style(frame)
        black_bbox = None
        red_bbox = None

        if fx is None:
            return None, None, None, None

        # 使用精确的轮廓信息构建边界框
        if best_contour is not None:
            # 计算轮廓的精确边界
            contour_points = best_contour.reshape(-1, 2)
            min_x = np.min(contour_points[:, 0])
            max_x = np.max(contour_points[:, 0])
            min_y = np.min(contour_points[:, 1])
            max_y = np.max(contour_points[:, 1])

            # 构建包含轮廓信息的边界框 (x1, y1, x2, y2, contour)
            black_bbox = (int(min_x), int(min_y), int(max_x), int(max_y), best_contour)
        else:
            # 备用方案：使用简单的边界框检测
            small_frame = cv2.resize(frame, (320, 240))
            gray = cv2.cvtColor(small_frame, cv2.COLOR_BGR2GRAY)
            _, binary = cv2.threshold(gray, self.binary_threshold, 255, cv2.THRESH_BINARY_INV)
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                area = cv2.contourArea(contour)
                if 200 < area < 8000:
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = max(w, h) / min(w, h) if min(w, h) > 0 else 0

                    if 0.8 <= aspect_ratio <= 2.0:
                        # 精确映射回原图坐标 (320x240 -> 640x480)
                        scale_x = 640.0 / 320.0
                        scale_y = 480.0 / 240.0
                        black_bbox = (int(x * scale_x), int(y * scale_y),
                                    int((x + w) * scale_x), int((y + h) * scale_y))
                        break

        # 2. 在黑框内检测红色靶心
        roi_size = 100
        x1 = max(0, fx - roi_size//2)
        y1 = max(0, fy - roi_size//2)
        x2 = min(self.frame_width, fx + roi_size//2)
        y2 = min(self.frame_height, fy + roi_size//2)

        roi = frame[y1:y2, x1:x2]

        # 3. 在ROI内检测红色靶心
        target_center = self.detect_red_target_in_roi(roi)
        if target_center[0] is not None and target_center[1] is not None:
            global_x = target_center[0] + x1
            global_y = target_center[1] + y1

            # 估算红色靶心的边界框（假设是圆形，半径约15像素）
            red_bbox = (global_x - 15, global_y - 15, global_x + 15, global_y + 15)

            return global_x, global_y, black_bbox, red_bbox

        # 4. 没找到靶心，返回黑框中心
        return fx, fy, black_bbox, None

class RaspberryPiGimbalController:
    """树莓派云台控制器"""
    def __init__(self, serial_port='/dev/ttyS0', baudrate=115200, camera_id=0):
        # 串口通信
        try:
            self.serial_conn = serial.Serial(serial_port, baudrate, timeout=0.1)
            print(f"[串口] 连接成功: {serial_port} @ {baudrate}")
        except Exception as e:
            print(f"[串口] 连接失败: {e}")
            self.serial_conn = None

        # 通信协议
        self.protocol = GimbalControlProtocol()

        # 视觉系统
        self.vision = RaspberryPiVisionSystem(camera_id)

        # 控制参数（移除PID，改为简单比例控制）
        self.tolerance = 15  # 瞄准容差（像素）
        self.fast_mode_factor = 1.0    # 2秒模式：直接发送偏差值
        self.precise_mode_factor = 1.0  # 4秒模式：直接发送偏差值

        # 多线程控制
        self.running = False
        self.frame_queue = Queue(maxsize=2)
        self.result_queue = Queue(maxsize=1)

        # 当前模式
        self.current_mode = 0

    def calculate_target_error(self, target_x, target_y):
        """计算目标偏差值（树莓派只计算偏差，PID交给单片机）"""
        error_x = target_x - self.vision.frame_center_x
        error_y = target_y - self.vision.frame_center_y

        # 检查是否已瞄准
        is_aimed = abs(error_x) <= self.tolerance and abs(error_y) <= self.tolerance

        return error_x, error_y, is_aimed

    def control_gimbal_to_target(self, target_x, target_y, mode=6):
        """控制云台瞄准目标（方案一：发送偏差值给单片机做PID）"""
        if not self.serial_conn:
            return False

        try:
            # 1. 计算目标偏差
            error_x, error_y, is_aimed = self.calculate_target_error(target_x, target_y)

            if is_aimed:
                # 已瞄准，发送停止命令
                stop_packet = self.protocol.create_stop_packet()
                self.serial_conn.write(stop_packet)
                print(f"[云台控制] 已瞄准目标: ({target_x}, {target_y}), 偏差: ({error_x}, {error_y})")
                return True
            else:
                # 发送偏差值给单片机，让单片机做PID控制
                error_packet = self.protocol.create_target_error_packet(error_x, error_y, mode)
                self.serial_conn.write(error_packet)
                print(f"[云台控制] 发送偏差值: error_x={error_x}, error_y={error_y}, mode={mode}")
                return False

        except Exception as e:
            print(f"[云台控制] 控制失败: {e}")
            return False

    def fire_command(self):
        """发送发射命令"""
        if not self.serial_conn:
            return False

        try:
            fire_packet = self.protocol.create_fire_packet()
            self.serial_conn.write(fire_packet)
            print("[云台控制] 发射命令已发送")
            return True
        except Exception as e:
            print(f"[云台控制] 发射失败: {e}")
            return False

    def mode_6_fast_aiming(self):
        """模式6：2秒内快速瞄准"""
        print("=" * 50)
        print("启动模式6：2秒快速瞄准模式")
        print("=" * 50)

        start_time = time.time()
        max_time = 2.0
        frame_count = 0

        while time.time() - start_time < max_time:
            ret, frame = self.vision.cap.read()
            if not ret:
                continue

            frame_count += 1

            # 快速检测目标
            target_x, target_y = self.vision.detect_black_frame_fast(frame)

            if target_x is not None:
                print(f"[模式6] 检测到目标: ({target_x}, {target_y})")

                # 快速控制云台（模式6）
                is_aimed = self.control_gimbal_to_target(
                    target_x, target_y, mode=6)

                if is_aimed:
                    # 瞄准成功，短暂稳定后发射
                    time.sleep(0.1)
                    success = self.fire_command()
                    elapsed = time.time() - start_time
                    print(f"[模式6] 瞄准成功！用时: {elapsed:.2f}秒, 处理帧数: {frame_count}")
                    return success
            else:
                print(f"[模式6] 第{frame_count}帧：未检测到目标")

            time.sleep(0.05)  # 50ms循环间隔

        elapsed = time.time() - start_time
        print(f"[模式6] 超时失败，用时: {elapsed:.2f}秒, 处理帧数: {frame_count}")
        return False

    def mode_7_precise_aiming(self):
        """模式7：4秒内精确瞄准"""
        print("=" * 50)
        print("启动模式7：4秒精确瞄准模式")
        print("=" * 50)

        start_time = time.time()
        max_time = 4.0
        frame_count = 0

        stable_count = 0
        required_stable = 3  # 需要连续3次稳定瞄准

        while time.time() - start_time < max_time:
            ret, frame = self.vision.cap.read()
            if not ret:
                continue

            frame_count += 1

            # 精确检测目标（先黑框，再靶心）
            target_x, target_y = self.vision.detect_target_precise(frame)

            if target_x is not None:
                print(f"[模式7] 检测到目标: ({target_x}, {target_y})")

                # 精确控制云台（模式7）
                is_aimed = self.control_gimbal_to_target(
                    target_x, target_y, mode=7)

                if is_aimed:
                    stable_count += 1
                    print(f"[模式7] 稳定瞄准 {stable_count}/{required_stable}")

                    if stable_count >= required_stable:
                        # 连续稳定瞄准，发射
                        success = self.fire_command()
                        elapsed = time.time() - start_time
                        print(f"[模式7] 精确瞄准成功！用时: {elapsed:.2f}秒, 处理帧数: {frame_count}")
                        return success
                else:
                    stable_count = 0  # 重置稳定计数
            else:
                print(f"[模式7] 第{frame_count}帧：未检测到目标")
                stable_count = 0

            time.sleep(0.1)  # 100ms循环间隔

        elapsed = time.time() - start_time
        print(f"[模式7] 超时失败，用时: {elapsed:.2f}秒, 处理帧数: {frame_count}")
        return False

    def threshold_adjustment_window(self):
        """阈值调节窗口 - 适应不同比赛场地"""
        print("=" * 60)
        print("阈值调节模式 - 适应比赛场地光照条件")
        print("=" * 60)
        print("窗口说明:")
        print("  'Threshold Adjustment' - 主调节窗口（彩色图像+检测结果）")
        print("  'Binary Debug' - 二值化调试窗口（黑白图像）")
        print("控制说明:")
        print("  'q' - 退出调节模式")
        print("  's' - 保存当前参数")
        print("  'r' - 重置为默认参数")
        print("  滑动条 - 实时调节检测参数")
        print("调试技巧:")
        print("  - 在Binary Debug窗口中，白色区域是检测到的对象")
        print("  - 调整Binary Threshold让黑胶带变成白色")
        print("  - 调整面积参数过滤掉干扰物体")
        print("")
        print("远距离优化功能:")
        print("  ✓ 边缘增强预处理")
        print("  ✓ 多阈值自适应检测")
        print("  ✓ 时间稳定性滤波")
        print("  ✓ 轮廓近似降噪")
        print("=" * 60)

        # 创建调节窗口
        cv2.namedWindow("Threshold Adjustment", cv2.WINDOW_NORMAL)
        cv2.resizeWindow("Threshold Adjustment", 800, 600)

        # 创建二值化调试窗口
        cv2.namedWindow("Binary Debug", cv2.WINDOW_NORMAL)
        cv2.resizeWindow("Binary Debug", 640, 480)

        # 创建参数调节滑动条
        cv2.createTrackbar("Binary Threshold", "Threshold Adjustment",
                          self.vision.binary_threshold, 255,
                          lambda val: setattr(self.vision, 'binary_threshold', val))

        cv2.createTrackbar("Min Area/10", "Threshold Adjustment",
                          self.vision.min_area//10, 1000,
                          lambda val: setattr(self.vision, 'min_area', val*10))

        cv2.createTrackbar("Max Area/100", "Threshold Adjustment",
                          self.vision.max_area//100, 1000,
                          lambda val: setattr(self.vision, 'max_area', val*100))

        cv2.createTrackbar("Tolerance", "Threshold Adjustment",
                          self.tolerance, 50,
                          lambda val: setattr(self, 'tolerance', val))

        # 保存原始参数
        original_params = {
            'binary_threshold': self.vision.binary_threshold,
            'min_area': self.vision.min_area,
            'max_area': self.vision.max_area,
            'tolerance': self.tolerance
        }

        detection_count = 0
        total_frames = 0

        while True:
            ret, frame = self.vision.cap.read()
            if not ret:
                continue

            total_frames += 1

            # 检测目标并获取二值化图像用于调试
            black_x, black_y, binary_image, bbox_info = self.vision.detect_black_frame_with_binary_debug(frame)
            red_x, red_y = None, None

            if black_x is not None:
                detection_count += 1
                # 在黑框周围检测红色目标
                roi_size = 100
                x1 = max(0, black_x - roi_size)
                y1 = max(0, black_y - roi_size)
                x2 = min(frame.shape[1], black_x + roi_size)
                y2 = min(frame.shape[0], black_y + roi_size)
                roi = frame[y1:y2, x1:x2]

                red_result = self.vision.detect_red_target_in_roi(roi)
                if red_result[0] is not None and red_result[1] is not None:
                    red_x = red_result[0] + x1
                    red_y = red_result[1] + y1

            # 绘制调节界面
            display_frame = frame.copy()

            # 绘制画面中心
            cv2.circle(display_frame, (self.vision.frame_center_x, self.vision.frame_center_y),
                      8, (0, 255, 255), 2)
            cv2.putText(display_frame, "CENTER",
                       (self.vision.frame_center_x + 15, self.vision.frame_center_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

            # 绘制容差范围
            cv2.rectangle(display_frame,
                        (self.vision.frame_center_x - self.tolerance,
                         self.vision.frame_center_y - self.tolerance),
                        (self.vision.frame_center_x + self.tolerance,
                         self.vision.frame_center_y + self.tolerance),
                        (255, 255, 0), 2)

            # 绘制检测结果
            if black_x is not None:
                # 绘制中心点
                cv2.circle(display_frame, (black_x, black_y), 5, (0, 255, 0), -1)

                # 绘制绿色边界框
                if bbox_info is not None:
                    if len(bbox_info) == 5:  # 包含轮廓信息的格式
                        x1, y1, x2, y2, contour = bbox_info
                        # 绘制精确轮廓
                        cv2.drawContours(display_frame, [contour], -1, (0, 255, 0), 3)
                        # 同时绘制边界框作为参考
                        cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 1)
                        print(f"绘制精确轮廓边界框: ({x1}, {y1}) -> ({x2}, {y2})")
                    elif len(bbox_info) == 4:  # 传统格式
                        x1, y1, x2, y2 = bbox_info
                        cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                        print(f"绘制传统边界框: ({x1}, {y1}) -> ({x2}, {y2})")
                    else:
                        print(f"bbox_info 格式不正确，长度为 {len(bbox_info)}")
                        x1, y1, x2, y2 = 0, 0, 100, 100  # 默认值
                else:
                    print("bbox_info 为 None")
                    x1, y1, x2, y2 = 0, 0, 100, 100  # 默认值

                # 在框的左上角显示标签
                cv2.putText(display_frame, f"BLACK FRAME",
                           (x1, y1 - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

                # 在框的右下角显示坐标
                cv2.putText(display_frame, f"({black_x},{black_y})",
                           (x2 - 100, y2 + 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

            if red_x is not None:
                cv2.circle(display_frame, (red_x, red_y), 8, (0, 0, 255), -1)
                cv2.putText(display_frame, f"RED({red_x},{red_y})",
                           (red_x + 15, red_y + 15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

                # 绘制瞄准线
                cv2.line(display_frame,
                        (self.vision.frame_center_x, self.vision.frame_center_y),
                        (red_x, red_y), (255, 0, 0), 2)

                # 显示偏差
                error_x = red_x - self.vision.frame_center_x
                error_y = red_y - self.vision.frame_center_y
                cv2.putText(display_frame, f"Error: ({error_x}, {error_y})",
                           (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # 显示当前参数
            cv2.putText(display_frame, f"Binary Threshold: {self.vision.binary_threshold}",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(display_frame, f"Min Area: {self.vision.min_area}",
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(display_frame, f"Max Area: {self.vision.max_area}",
                       (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(display_frame, f"Tolerance: {self.tolerance}",
                       (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # 显示检测统计
            detection_rate = (detection_count / total_frames * 100) if total_frames > 0 else 0
            cv2.putText(display_frame, f"Detection Rate: {detection_rate:.1f}%",
                       (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            # 显示远距离检测状态
            if hasattr(self.vision, 'last_detection_area') and self.vision.last_detection_area:
                if self.vision.last_detection_area < 1000:
                    cv2.putText(display_frame, "Distance: FAR (Small Target)",
                               (10, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                elif self.vision.last_detection_area < 5000:
                    cv2.putText(display_frame, "Distance: MEDIUM",
                               (10, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
                else:
                    cv2.putText(display_frame, "Distance: CLOSE",
                               (10, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            # 显示稳定性信息
            if hasattr(self.vision, 'detection_history'):
                valid_detections = len([d for d in self.vision.detection_history if d is not None])
                stability_ratio = valid_detections / len(self.vision.detection_history) if self.vision.detection_history else 0
                stability_color = (0, 255, 0) if stability_ratio > 0.6 else (0, 255, 255) if stability_ratio > 0.3 else (0, 0, 255)
                cv2.putText(display_frame, f"Stability: {stability_ratio:.1%}",
                           (10, 240), cv2.FONT_HERSHEY_SIMPLEX, 0.7, stability_color, 2)

            # 显示主窗口
            cv2.imshow("Threshold Adjustment", display_frame)

            # 显示二值化调试窗口
            if binary_image is not None:
                # 在二值化图像上添加参数信息
                binary_with_info = binary_image.copy()
                cv2.putText(binary_with_info, f"Threshold: {self.vision.binary_threshold}",
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, 255, 2)
                cv2.putText(binary_with_info, f"Min Area: {self.vision.min_area}",
                           (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, 255, 2)
                cv2.putText(binary_with_info, f"Max Area: {self.vision.max_area}",
                           (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, 255, 2)
                cv2.putText(binary_with_info, "WHITE=Detected Objects",
                           (10, 450), cv2.FONT_HERSHEY_SIMPLEX, 0.6, 255, 2)

                cv2.imshow("Binary Debug", binary_with_info)

            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # 保存参数
                self.save_parameters()
                print("参数已保存到配置文件")
            elif key == ord('r'):
                # 重置参数
                self.vision.binary_threshold = original_params['binary_threshold']
                self.vision.min_area = original_params['min_area']
                self.vision.max_area = original_params['max_area']
                self.tolerance = original_params['tolerance']

                # 更新滑动条
                cv2.setTrackbarPos("Binary Threshold", "Threshold Adjustment", self.vision.binary_threshold)
                cv2.setTrackbarPos("Min Area/10", "Threshold Adjustment", self.vision.min_area//10)
                cv2.setTrackbarPos("Max Area/100", "Threshold Adjustment", self.vision.max_area//100)
                cv2.setTrackbarPos("Tolerance", "Threshold Adjustment", self.tolerance)
                print("参数已重置为默认值")

        cv2.destroyWindow("Threshold Adjustment")
        cv2.destroyWindow("Binary Debug")
        print("阈值调节完成")

    def save_parameters(self):
        """保存当前参数到配置文件"""
        try:
            import json
            config = {
                "detection": {
                    "binary_threshold": self.vision.binary_threshold,
                    "min_area": self.vision.min_area,
                    "max_area": self.vision.max_area,
                    "tolerance": self.tolerance
                },
                "timestamp": time.time()
            }

            with open("current_config.json", "w") as f:
                json.dump(config, f, indent=4)

            print(f"参数已保存: 阈值={self.vision.binary_threshold}, 面积={self.vision.min_area}-{self.vision.max_area}, 容差={self.tolerance}")

        except Exception as e:
            print(f"保存参数失败: {e}")

    def test_vision_system(self):
        """测试视觉系统"""
        print("=" * 50)
        print("视觉系统测试模式")
        print("按 'q' 退出, 按 's' 截图保存")
        print("=" * 50)

        while True:
            ret, frame = self.vision.cap.read()
            if not ret:
                continue

            # 检测目标（带边界框信息）
            target_x, target_y, black_bbox, red_bbox = self.vision.detect_target_with_bbox(frame)

            # 绘制检测结果
            display_frame = frame.copy()

            # 绘制画面中心
            cv2.circle(display_frame,
                      (self.vision.frame_center_x, self.vision.frame_center_y),
                      5, (0, 255, 255), -1)
            cv2.putText(display_frame, "CENTER",
                       (self.vision.frame_center_x + 10, self.vision.frame_center_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

            # 绘制检测到的目标
            if target_x is not None:
                # 绘制黑框（绿色边界，精确贴合轮廓）
                if black_bbox is not None:
                    try:
                        if len(black_bbox) == 5:  # 包含轮廓信息
                            x1, y1, x2, y2, contour = black_bbox

                            # 方法1：绘制精确的轮廓边界（最贴合）
                            if contour is not None and len(contour) > 0:
                                cv2.drawContours(display_frame, [contour], -1, (0, 255, 0), 3)

                            # 方法2：同时绘制一个稍微向内收缩的矩形框作为补充
                            # 这样可以确保即使轮廓不完整也有清晰的边界
                            margin = 5  # 向内收缩5像素
                            inner_x1 = x1 + margin
                            inner_y1 = y1 + margin
                            inner_x2 = x2 - margin
                            inner_y2 = y2 - margin
                            if inner_x2 > inner_x1 and inner_y2 > inner_y1:
                                cv2.rectangle(display_frame, (inner_x1, inner_y1), (inner_x2, inner_y2), (0, 255, 0), 2)

                            cv2.putText(display_frame, "BLACK FRAME",
                                       (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                        elif len(black_bbox) == 4:  # 旧格式，只有矩形坐标
                            x1, y1, x2, y2 = black_bbox
                            cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                            cv2.putText(display_frame, "BLACK FRAME",
                                       (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                        else:
                            print(f"警告：black_bbox格式不正确，长度为{len(black_bbox)}")
                    except Exception as e:
                        print(f"绘制黑框时出错: {e}")
                        # 备用绘制方法
                        if len(black_bbox) >= 4:
                            x1, y1, x2, y2 = black_bbox[:4]
                            cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    except Exception as e:
                        print(f"绘制黑框时出错: {e}, black_bbox类型: {type(black_bbox)}, 长度: {len(black_bbox) if hasattr(black_bbox, '__len__') else 'N/A'}")
                        # 备用绘制方法
                        if hasattr(black_bbox, '__len__') and len(black_bbox) >= 4:
                            x1, y1, x2, y2 = black_bbox[:4]
                            cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)

                # 绘制红色靶心（红色圆圈）
                if red_bbox is not None:
                    cv2.circle(display_frame, (target_x, target_y), 15, (0, 0, 255), 2)
                    cv2.circle(display_frame, (target_x, target_y), 3, (0, 0, 255), -1)
                    cv2.putText(display_frame, "RED TARGET",
                               (target_x + 20, target_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
                else:
                    # 只检测到黑框，显示黑框中心
                    cv2.circle(display_frame, (target_x, target_y), 8, (0, 255, 0), -1)
                    cv2.putText(display_frame, "BLACK CENTER",
                               (target_x + 15, target_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                # 绘制偏差线
                cv2.line(display_frame,
                        (self.vision.frame_center_x, self.vision.frame_center_y),
                        (target_x, target_y), (255, 0, 0), 2)

                # 显示偏差信息
                error_x = target_x - self.vision.frame_center_x
                error_y = target_y - self.vision.frame_center_y
                cv2.putText(display_frame, f"Error: ({error_x}, {error_y})",
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            else:
                cv2.putText(display_frame, "NO TARGET DETECTED",
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

            # 显示帧率
            cv2.putText(display_frame, f"FPS: {int(self.vision.cap.get(cv2.CAP_PROP_FPS))}",
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            cv2.imshow("Raspberry Pi Vision Test", display_frame)

            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                filename = f"capture_{int(time.time())}.jpg"
                cv2.imwrite(filename, display_frame)
                print(f"截图保存: {filename}")

        cv2.destroyAllWindows()

    def run_challenge_mode(self, mode):
        """运行挑战模式"""
        if mode == 6:
            return self.mode_6_fast_aiming()
        elif mode == 7:
            return self.mode_7_precise_aiming()
        else:
            print(f"无效模式: {mode}")
            return False

    def cleanup(self):
        """清理资源"""
        if self.vision.cap:
            self.vision.cap.release()
        if self.serial_conn:
            self.serial_conn.close()
        cv2.destroyAllWindows()
        print("[系统] 资源清理完成")

def main():
    """主程序"""
    print("=" * 60)
    print("树莓派云台瞄准控制系统")
    print("=" * 60)

    # 初始化控制器
    try:
        controller = RaspberryPiGimbalController(
            serial_port='/dev/ttyS0',  # 根据实际情况修改
            baudrate=115200,
            camera_id=0
        )
    except Exception as e:
        print(f"初始化失败: {e}")
        return

    try:
        while True:
            print("\n" + "=" * 50)
            print("树莓派云台瞄准控制系统")
            print("=" * 50)
            print("请选择模式:")
            print("1. 阈值调节模式 (适应比赛场地)")
            print("2. 视觉系统测试")
            print("3. 模式6 - 2秒快速瞄准")
            print("4. 模式7 - 4秒精确瞄准")
            print("5. 退出程序")
            print("=" * 50)

            choice = input("请输入选择 (1-5): ").strip()

            if choice == '1':
                controller.threshold_adjustment_window()
            elif choice == '2':
                controller.test_vision_system()
            elif choice == '3':
                result = controller.run_challenge_mode(6)
                print(f"模式6结果: {'成功' if result else '失败'}")
            elif choice == '4':
                result = controller.run_challenge_mode(7)
                print(f"模式7结果: {'成功' if result else '失败'}")
            elif choice == '5':
                break
            else:
                print("无效选择，请重新输入")

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
    finally:
        controller.cleanup()

if __name__ == "__main__":
    main()